'use client'

import React from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { ChevronRight, Home } from 'lucide-react'
import { cn } from '@/lib/utils'

interface BreadcrumbItem {
  label: string
  href: string
  icon?: React.ReactNode
}

interface EnhancedBreadcrumbProps {
  items?: BreadcrumbItem[]
  className?: string
  showHome?: boolean
  maxItems?: number
}

export function EnhancedBreadcrumb({ 
  items, 
  className, 
  showHome = true, 
  maxItems = 4 
}: EnhancedBreadcrumbProps) {
  const pathname = usePathname()
  
  // Auto-generate breadcrumbs from pathname if items not provided
  const breadcrumbItems = items || generateBreadcrumbsFromPath(pathname)
  
  // Truncate items if too many
  const displayItems = breadcrumbItems.length > maxItems 
    ? [
        ...breadcrumbItems.slice(0, 1),
        { label: '...', href: '#', icon: null },
        ...breadcrumbItems.slice(-2)
      ]
    : breadcrumbItems

  return (
    <nav 
      aria-label="Breadcrumb navigation"
      className={cn("flex items-center space-x-1 text-sm", className)}
    >
      {showHome && (
        <>
          <Link
            href="/dashboard"
            className="flex items-center gap-1 text-text-tertiary hover:text-text-primary transition-colors duration-200 focus-ring rounded-md px-2 py-1"
            aria-label="Go to dashboard"
          >
            <Home className="w-4 h-4" />
            <span className="sr-only">Dashboard</span>
          </Link>
          {breadcrumbItems.length > 0 && (
            <ChevronRight className="w-4 h-4 text-text-tertiary" aria-hidden="true" />
          )}
        </>
      )}
      
      {displayItems.map((item, index) => {
        const isLast = index === displayItems.length - 1
        const isEllipsis = item.label === '...'
        
        return (
          <React.Fragment key={`${item.href}-${index}`}>
            {isEllipsis ? (
              <span className="text-text-tertiary px-2 py-1">...</span>
            ) : isLast ? (
              <span 
                className="flex items-center gap-1 text-text-primary font-medium px-2 py-1"
                aria-current="page"
              >
                {item.icon}
                {item.label}
              </span>
            ) : (
              <Link
                href={item.href}
                className="flex items-center gap-1 text-text-tertiary hover:text-text-primary transition-colors duration-200 focus-ring rounded-md px-2 py-1"
              >
                {item.icon}
                {item.label}
              </Link>
            )}
            
            {!isLast && (
              <ChevronRight className="w-4 h-4 text-text-tertiary" aria-hidden="true" />
            )}
          </React.Fragment>
        )
      })}
    </nav>
  )
}

function generateBreadcrumbsFromPath(pathname: string): BreadcrumbItem[] {
  const segments = pathname.split('/').filter(Boolean)
  const breadcrumbs: BreadcrumbItem[] = []
  
  // Route mapping for better labels
  const routeLabels: Record<string, string> = {
    'dashboard': 'Dashboard',
    'transactions': 'Transactions',
    'goals': 'Goals',
    'ai-chat': 'AI Assistant',
    'recurring-payments': 'Recurring Payments',
    'loans-debts': 'Loans & Debts',
    'settings': 'Settings',
    'profile': 'Profile',
    'auth': 'Authentication',
    'signin': 'Sign In',
    'signup': 'Sign Up',
    'onboarding': 'Getting Started'
  }
  
  let currentPath = ''
  
  segments.forEach((segment, index) => {
    currentPath += `/${segment}`
    const label = routeLabels[segment] || segment.charAt(0).toUpperCase() + segment.slice(1)
    
    breadcrumbs.push({
      label,
      href: currentPath
    })
  })
  
  return breadcrumbs
}

// Usage Examples:
/*
// Auto-generated from current path
<EnhancedBreadcrumb />

// Custom breadcrumbs
<EnhancedBreadcrumb 
  items={[
    { label: 'Transactions', href: '/transactions' },
    { label: 'Categories', href: '/transactions/categories' },
    { label: 'Food & Dining', href: '/transactions/categories/food' }
  ]}
/>

// With icons
<EnhancedBreadcrumb 
  items={[
    { label: 'Goals', href: '/goals', icon: <Target className="w-4 h-4" /> },
    { label: 'Savings Goal', href: '/goals/123', icon: <PiggyBank className="w-4 h-4" /> }
  ]}
/>
*/
