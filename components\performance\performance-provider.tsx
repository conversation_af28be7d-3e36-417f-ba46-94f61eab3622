'use client'

import { useEffect, useState } from 'react'
import { useServiceWorker, usePWAInstall, useNetworkStatus } from './service-worker'
import { usePerformanceMonitor, preloadCriticalResources } from './performance-monitor'

interface PerformanceProviderProps {
  children: React.ReactNode
}

export function PerformanceProvider({ children }: PerformanceProviderProps) {
  // Initialize service worker
  useServiceWorker()
  
  // Initialize PWA install prompt
  const { installPWA } = usePWAInstall()
  
  // Monitor network status
  const isOnline = useNetworkStatus()
  
  // Monitor performance metrics
  const performanceMetrics = usePerformanceMonitor()

  useEffect(() => {
    // Report initial performance metrics
    if (typeof window !== 'undefined') {
      // Wait for page load to report metrics
      window.addEventListener('load', () => {
        setTimeout(() => {
          console.log('Performance Metrics:', performanceMetrics)
        }, 1000)
      })
    }
  }, [performanceMetrics])

  // Show offline indicator
  useEffect(() => {
    if (!isOnline) {
      console.log('App is offline - showing offline indicator')
      // You can show an offline banner here
    }
  }, [isOnline])

  return (
    <>
      {children}
      
      {/* Offline Indicator */}
      {!isOnline && (
        <div 
          className="fixed bottom-4 left-4 right-4 bg-warning text-warning-foreground px-4 py-2 rounded-md shadow-lg z-50 text-center"
          role="alert"
          aria-live="polite"
        >
          You are currently offline. Some features may be limited.
        </div>
      )}
      
      {/* PWA Install Prompt - You can customize this */}
      <PWAInstallPrompt onInstall={installPWA} />
    </>
  )
}

interface PWAInstallPromptProps {
  onInstall: () => void
}

function PWAInstallPrompt({ onInstall }: PWAInstallPromptProps) {
  const [showPrompt, setShowPrompt] = useState(false)

  useEffect(() => {
    // Check if app is already installed
    const isStandalone = window.matchMedia('(display-mode: standalone)').matches
    const isInWebAppiOS = (window.navigator as any).standalone === true
    const isInstalled = isStandalone || isInWebAppiOS

    if (!isInstalled) {
      // Show install prompt after user has been on the site for a while
      const timer = setTimeout(() => {
        setShowPrompt(true)
      }, 30000) // Show after 30 seconds

      return () => clearTimeout(timer)
    }
  }, [])

  if (!showPrompt) return null

  return (
    <div className="fixed bottom-4 right-4 bg-primary text-primary-foreground p-4 rounded-lg shadow-lg z-50 max-w-sm">
      <div className="flex flex-col gap-3">
        <div>
          <h3 className="font-semibold text-sm">Install Finance Tracker</h3>
          <p className="text-xs opacity-90">
            Install our app for a better experience with offline access.
          </p>
        </div>
        <div className="flex gap-2">
          <button
            onClick={() => {
              onInstall()
              setShowPrompt(false)
            }}
            className="bg-primary-foreground text-primary px-3 py-1 rounded text-xs font-medium hover:opacity-90 transition-opacity"
          >
            Install
          </button>
          <button
            onClick={() => setShowPrompt(false)}
            className="text-primary-foreground px-3 py-1 rounded text-xs hover:opacity-70 transition-opacity"
          >
            Later
          </button>
        </div>
      </div>
    </div>
  )
}

// Performance monitoring component for development
export function PerformanceMonitor() {
  const performanceMetrics = usePerformanceMonitor()
  const isOnline = useNetworkStatus()

  if (process.env.NODE_ENV !== 'development') {
    return null
  }

  return (
    <div className="fixed top-4 right-4 bg-black text-white p-2 rounded text-xs font-mono z-50 max-w-xs">
      <div className="space-y-1">
        <div>Status: {isOnline ? '🟢 Online' : '🔴 Offline'}</div>
        {performanceMetrics.fcp && (
          <div>FCP: {Math.round(performanceMetrics.fcp)}ms</div>
        )}
        {performanceMetrics.lcp && (
          <div>LCP: {Math.round(performanceMetrics.lcp)}ms</div>
        )}
        {performanceMetrics.fid && (
          <div>FID: {Math.round(performanceMetrics.fid)}ms</div>
        )}
        {performanceMetrics.cls && (
          <div>CLS: {performanceMetrics.cls.toFixed(3)}</div>
        )}
        {performanceMetrics.ttfb && (
          <div>TTFB: {Math.round(performanceMetrics.ttfb)}ms</div>
        )}
      </div>
    </div>
  )
}


