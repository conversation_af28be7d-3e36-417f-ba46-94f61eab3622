'use client'

import * as React from "react"
import { TrendingUp } from "lucide-react"
import { <PERSON>, <PERSON><PERSON><PERSON> } from "recharts"
import { useAuth } from '@/lib/contexts/auth-context'
import { useState, useEffect } from 'react'
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart"

const chartData = [
  { category: "Food & Dining", amount: 450, fill: "var(--chart-1)" },
  { category: "Transportation", amount: 280, fill: "var(--chart-2)" },
  { category: "Shopping", amount: 320, fill: "var(--chart-3)" },
  { category: "Utilities", amount: 180, fill: "var(--chart-4)" },
  { category: "Entertainment", amount: 150, fill: "var(--chart-5)" },
  { category: "Health", amount: 120, fill: "var(--chart-1)" },
]

const chartConfig = {
  amount: {
    label: "Amount",
  },
  food: {
    label: "Food & Dining",
    color: "var(--chart-1)",
  },
  transport: {
    label: "Transportation",
    color: "var(--chart-2)",
  },
  shopping: {
    label: "Shopping",
    color: "var(--chart-3)",
  },
  utilities: {
    label: "Utilities",
    color: "var(--chart-4)",
  },
  entertainment: {
    label: "Entertainment",
    color: "var(--chart-5)",
  },
  health: {
    label: "Health",
    color: "var(--chart-1)",
  },
} satisfies ChartConfig

export function SpendingCategoriesChart() {
  const { profile } = useAuth()
  const [loading, setLoading] = useState(true)
  
  const currency = profile?.currency_code || 'KWD'

  useEffect(() => {
    // Simulate loading for realistic UX
    const timer = setTimeout(() => setLoading(false), 500)
    return () => clearTimeout(timer)
  }, [])

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value)
  }

  const totalSpending = chartData.reduce((sum, item) => sum + item.amount, 0)
  const topCategory = chartData.reduce((prev, current) => 
    prev.amount > current.amount ? prev : current
  )

  if (loading) {
    return (
      <Card className="flex flex-col">
        <CardHeader className="items-center pb-0">
          <CardTitle>Spending by Category</CardTitle>
          <CardDescription>Loading spending data...</CardDescription>
        </CardHeader>
        <CardContent className="flex-1 pb-0">
          <div className="mx-auto aspect-square max-h-[250px] w-full animate-pulse bg-gray-200 rounded-full"></div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="flex flex-col">
      <CardHeader className="items-center pb-0">
        <CardTitle>Spending by Category</CardTitle>
        <CardDescription>Current month breakdown</CardDescription>
      </CardHeader>
      <CardContent className="flex-1 pb-0">
        <ChartContainer
          config={chartConfig}
          className="mx-auto aspect-square max-h-[250px]"
        >
          <PieChart>
            <ChartTooltip
              cursor={false}
              content={<ChartTooltipContent hideLabel />}
            />
            <Pie
              data={chartData}
              dataKey="amount"
              nameKey="category"
              stroke="0"
            />
          </PieChart>
        </ChartContainer>
      </CardContent>
      <CardFooter className="flex-col gap-2 text-sm">
        <div className="flex items-center gap-2 leading-none font-medium">
          {topCategory.category} is your largest expense <TrendingUp className="h-4 w-4" />
        </div>
        <div className="text-muted-foreground leading-none">
          Total spending: {formatCurrency(totalSpending)} this month
        </div>
      </CardFooter>
    </Card>
  )
}
