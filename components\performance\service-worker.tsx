'use client'

import { useEffect, useState } from 'react'

// Service Worker registration hook
export function useServiceWorker() {
  useEffect(() => {
    if (
      typeof window !== 'undefined' &&
      'serviceWorker' in navigator &&
      process.env.NODE_ENV === 'production'
    ) {
      registerServiceWorker()
    }
  }, [])
}

async function registerServiceWorker() {
  try {
    console.log('Registering service worker...')
    
    const registration = await navigator.serviceWorker.register('/sw.js', {
      scope: '/',
    })

    console.log('Service Worker registered successfully:', registration)

    // Handle service worker updates
    registration.addEventListener('updatefound', () => {
      const newWorker = registration.installing
      if (newWorker) {
        console.log('New service worker found, installing...')
        
        newWorker.addEventListener('statechange', () => {
          if (newWorker.state === 'installed') {
            if (navigator.serviceWorker.controller) {
              // New service worker available
              console.log('New service worker available')
              showUpdateNotification()
            } else {
              // Service worker installed for the first time
              console.log('Service worker installed for the first time')
              showInstallNotification()
            }
          }
        })
      }
    })

    // Listen for service worker messages
    navigator.serviceWorker.addEventListener('message', (event) => {
      console.log('Message from service worker:', event.data)
      
      if (event.data.type === 'CACHE_UPDATED') {
        showCacheUpdateNotification()
      }
    })

    // Check for waiting service worker
    if (registration.waiting) {
      showUpdateNotification()
    }

  } catch (error) {
    console.error('Service Worker registration failed:', error)
  }
}

function showUpdateNotification() {
  // You can implement a custom notification UI here
  if (confirm('A new version is available. Reload to update?')) {
    window.location.reload()
  }
}

function showInstallNotification() {
  console.log('App is ready for offline use')
  // You can show a toast notification here
}

function showCacheUpdateNotification() {
  console.log('App data has been updated in the background')
  // You can show a subtle notification here
}

// PWA install prompt hook
export function usePWAInstall() {
  useEffect(() => {
    let deferredPrompt: any = null

    const handleBeforeInstallPrompt = (e: Event) => {
      // Prevent the mini-infobar from appearing on mobile
      e.preventDefault()
      deferredPrompt = e
      console.log('PWA install prompt available')
      
      // You can show your custom install button here
      showInstallButton()
    }

    const handleAppInstalled = () => {
      console.log('PWA was installed')
      deferredPrompt = null
      hideInstallButton()
    }

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
    window.addEventListener('appinstalled', handleAppInstalled)

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
      window.removeEventListener('appinstalled', handleAppInstalled)
    }
  }, [])

  const installPWA = async () => {
    const deferredPrompt = (window as any).deferredPrompt
    if (deferredPrompt) {
      deferredPrompt.prompt()
      const { outcome } = await deferredPrompt.userChoice
      console.log(`User response to the install prompt: ${outcome}`)
      ;(window as any).deferredPrompt = null
    }
  }

  return { installPWA }
}

function showInstallButton() {
  // Implement showing install button
  console.log('Show PWA install button')
}

function hideInstallButton() {
  // Implement hiding install button
  console.log('Hide PWA install button')
}

// Network status hook
export function useNetworkStatus() {
  const [isOnline, setIsOnline] = useState(navigator?.onLine ?? true)

  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true)
      console.log('App is back online')
      // Trigger background sync
      if ('serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype) {
        navigator.serviceWorker.ready.then((registration) => {
          return registration.sync.register('background-sync')
        }).catch((error) => {
          console.error('Background sync registration failed:', error)
        })
      }
    }

    const handleOffline = () => {
      setIsOnline(false)
      console.log('App is offline')
    }

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [])

  return isOnline
}

// Cache management utilities
export const cacheManager = {
  // Clear all caches
  async clearAll() {
    if ('caches' in window) {
      const cacheNames = await caches.keys()
      await Promise.all(
        cacheNames.map(cacheName => caches.delete(cacheName))
      )
      console.log('All caches cleared')
    }
  },

  // Get cache size
  async getSize() {
    if ('caches' in window && 'storage' in navigator && 'estimate' in navigator.storage) {
      const estimate = await navigator.storage.estimate()
      return {
        quota: estimate.quota,
        usage: estimate.usage,
        usageDetails: estimate.usageDetails
      }
    }
    return null
  },

  // Preload critical resources
  async preloadCritical() {
    if ('caches' in window) {
      const cache = await caches.open('finance-tracker-critical-v1')
      const criticalResources = [
        '/',
        '/auth/signin',
        '/auth/signup',
        '/manifest.json'
      ]
      
      try {
        await cache.addAll(criticalResources)
        console.log('Critical resources preloaded')
      } catch (error) {
        console.error('Failed to preload critical resources:', error)
      }
    }
  }
}

// Background sync for offline actions
export function useOfflineSync() {
  const addOfflineAction = async (action: any) => {
    if ('indexedDB' in window) {
      // Store action in IndexedDB for later sync
      console.log('Storing offline action:', action)
      // Implement IndexedDB storage here
    }
  }

  const syncOfflineActions = async () => {
    if ('serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype) {
      try {
        const registration = await navigator.serviceWorker.ready
        await registration.sync.register('background-sync')
        console.log('Background sync registered')
      } catch (error) {
        console.error('Background sync registration failed:', error)
      }
    }
  }

  return { addOfflineAction, syncOfflineActions }
}

// Performance monitoring integration
export function reportPerformanceMetrics() {
  if ('serviceWorker' in navigator) {
    navigator.serviceWorker.ready.then((registration) => {
      // Send performance metrics to service worker
      registration.active?.postMessage({
        type: 'PERFORMANCE_METRICS',
        metrics: {
          // Add your performance metrics here
          timestamp: Date.now()
        }
      })
    })
  }
}


