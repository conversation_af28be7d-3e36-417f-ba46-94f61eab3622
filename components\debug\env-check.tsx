'use client'

import { useEffect, useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { CheckCircle, XCircle, AlertCircle } from 'lucide-react'

interface EnvStatus {
  name: string
  value: string | undefined
  isSet: boolean
  isPublic: boolean
}

export function EnvCheck() {
  const [envVars, setEnvVars] = useState<EnvStatus[]>([])

  useEffect(() => {
    const checkEnvVars = () => {
      const varsToCheck = [
        { name: 'NEXT_PUBLIC_SUPABASE_URL', isPublic: true },
        { name: 'NEXT_PUBLIC_SUPABASE_ANON_KEY', isPublic: true },
        { name: 'GOOGLE_GENERATIVE_AI_API_KEY', isPublic: false },
        { name: 'NODE_ENV', isPublic: true }
      ]

      const status = varsToCheck.map(({ name, isPublic }) => {
        const value = process.env[name]
        return {
          name,
          value: value ? (isPublic ? value.substring(0, 20) + '...' : '[HIDDEN]') : undefined,
          isSet: !!value,
          isPublic
        }
      })

      setEnvVars(status)
    }

    checkEnvVars()
  }, [])

  const getStatusIcon = (isSet: boolean) => {
    if (isSet) {
      return <CheckCircle className="w-4 h-4 text-green-500" />
    }
    return <XCircle className="w-4 h-4 text-red-500" />
  }

  const getStatusBadge = (isSet: boolean) => {
    if (isSet) {
      return <Badge variant="default" className="bg-green-100 text-green-800">Set</Badge>
    }
    return <Badge variant="destructive">Missing</Badge>
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <AlertCircle className="w-5 h-5" />
          Environment Variables Status
        </CardTitle>
        <CardDescription>
          Check if all required environment variables are properly configured
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {envVars.map((env) => (
            <div key={env.name} className="flex items-center justify-between p-3 border rounded-lg">
              <div className="flex items-center gap-3">
                {getStatusIcon(env.isSet)}
                <div>
                  <div className="font-medium">{env.name}</div>
                  <div className="text-sm text-gray-500">
                    {env.isPublic ? 'Public variable' : 'Server-only variable'}
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-3">
                {env.value && (
                  <code className="text-xs bg-gray-100 px-2 py-1 rounded">
                    {env.value}
                  </code>
                )}
                {getStatusBadge(env.isSet)}
              </div>
            </div>
          ))}
        </div>
        
        <div className="mt-6 p-4 bg-blue-50 rounded-lg">
          <h4 className="font-medium text-blue-900 mb-2">Troubleshooting Tips:</h4>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• Make sure your <code>.env.local</code> file is in the project root</li>
            <li>• Restart your development server after adding environment variables</li>
            <li>• Public variables must start with <code>NEXT_PUBLIC_</code></li>
            <li>• Check for typos in variable names</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  )
}
