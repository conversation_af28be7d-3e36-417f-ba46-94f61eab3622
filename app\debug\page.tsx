import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  Bug, 
  Eye, 
  Zap, 
  Settings, 
  CheckCircle, 
  AlertTriangle,
  Info,
  ExternalLink,
  Play,
  FileText
} from 'lucide-react'

export default function DebugDashboard() {
  const testingTools = [
    {
      title: 'Error Boundary Testing',
      description: 'Test error boundaries and error handling components',
      icon: Bug,
      href: '/debug/error-testing',
      status: 'ready',
      features: [
        'Component error boundaries',
        'Async error handling',
        'Error message components',
        'Recovery mechanisms'
      ]
    },
    {
      title: 'Accessibility Testing',
      description: 'Test accessibility features and ARIA compliance',
      icon: Eye,
      href: '/debug/accessibility-testing',
      status: 'ready',
      features: [
        'Keyboard navigation',
        'Screen reader support',
        'ARIA labels and descriptions',
        'Focus management'
      ]
    },
    {
      title: 'Performance Testing',
      description: 'Monitor bundle size and loading performance',
      icon: Zap,
      href: '/debug/performance-testing',
      status: 'ready',
      features: [
        'Bundle size analysis',
        'Lazy loading verification',
        'Dashboard performance',
        'Cache effectiveness'
      ]
    },
    {
      title: 'Environment Check',
      description: 'Verify environment variables and configuration',
      icon: Settings,
      href: '/debug/env',
      status: 'ready',
      features: [
        'Environment variables',
        'API key validation',
        'Configuration status',
        'Troubleshooting tips'
      ]
    },
    {
      title: 'Profile Cleanup',
      description: 'Fix profile data issues and inconsistencies',
      icon: Settings,
      href: '/debug/cleanup-profiles',
      status: 'ready',
      features: [
        'Profile data validation',
        'Email cleanup from names',
        'Data consistency checks',
        'Manual fixes'
      ]
    }
  ]

  const quickCommands = [
    {
      title: 'Build & Analyze Bundle',
      description: 'Build the app and analyze bundle size',
      command: 'npm run build && npm run analyze',
      icon: FileText
    },
    {
      title: 'Run Lighthouse Audit',
      description: 'Open DevTools → Lighthouse → Run audit',
      command: 'Open DevTools (F12) → Lighthouse tab',
      icon: Zap
    },
    {
      title: 'Test Keyboard Navigation',
      description: 'Use Tab key to navigate through the app',
      command: 'Press Tab to navigate, Shift+Tab to go back',
      icon: Eye
    }
  ]

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'ready':
        return <CheckCircle className="w-4 h-4 text-green-500" />
      case 'warning':
        return <AlertTriangle className="w-4 h-4 text-yellow-500" />
      default:
        return <Info className="w-4 h-4 text-blue-500" />
    }
  }

  return (
    <div className="container mx-auto py-8 max-w-6xl">
      <div className="mb-8 text-center">
        <h1 className="text-3xl font-bold mb-2">Testing & Debug Dashboard</h1>
        <p className="text-gray-600">
          Comprehensive testing tools to verify all fixes and improvements
        </p>
      </div>

      {/* Quick Status Overview */}
      <Alert className="mb-6">
        <CheckCircle className="w-4 h-4" />
        <AlertDescription>
          <strong>All systems ready!</strong> Error boundaries, accessibility features, and performance optimizations have been implemented. 
          Use the tools below to verify everything is working correctly.
        </AlertDescription>
      </Alert>

      {/* Testing Tools Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        {testingTools.map((tool) => {
          const Icon = tool.icon
          return (
            <Card key={tool.href} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Icon className="w-5 h-5" />
                  {tool.title}
                  {getStatusIcon(tool.status)}
                </CardTitle>
                <CardDescription>{tool.description}</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <ul className="text-sm space-y-1">
                  {tool.features.map((feature, index) => (
                    <li key={index} className="flex items-center gap-2">
                      <div className="w-1.5 h-1.5 bg-blue-500 rounded-full" />
                      {feature}
                    </li>
                  ))}
                </ul>
                <Link href={tool.href}>
                  <Button className="w-full">
                    <Play className="w-4 h-4 mr-2" />
                    Start Testing
                  </Button>
                </Link>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Quick Commands */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Quick Testing Commands</CardTitle>
          <CardDescription>
            Common commands and actions for testing the application
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {quickCommands.map((cmd, index) => {
              const Icon = cmd.icon
              return (
                <div key={index} className="p-4 border rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <Icon className="w-4 h-4" />
                    <h4 className="font-medium">{cmd.title}</h4>
                  </div>
                  <p className="text-sm text-gray-600 mb-2">{cmd.description}</p>
                  <code className="text-xs bg-gray-100 p-2 rounded block">
                    {cmd.command}
                  </code>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Testing Checklist */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Testing Checklist</CardTitle>
          <CardDescription>
            Complete this checklist to verify all improvements are working
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium mb-3 flex items-center gap-2">
                <Bug className="w-4 h-4" />
                Error Handling
              </h4>
              <div className="space-y-2 text-sm">
                <label className="flex items-center gap-2">
                  <input type="checkbox" className="rounded" />
                  Error boundaries catch component errors
                </label>
                <label className="flex items-center gap-2">
                  <input type="checkbox" className="rounded" />
                  Async errors are handled gracefully
                </label>
                <label className="flex items-center gap-2">
                  <input type="checkbox" className="rounded" />
                  Error messages are descriptive
                </label>
                <label className="flex items-center gap-2">
                  <input type="checkbox" className="rounded" />
                  Recovery options are available
                </label>
              </div>
            </div>

            <div>
              <h4 className="font-medium mb-3 flex items-center gap-2">
                <Eye className="w-4 h-4" />
                Accessibility
              </h4>
              <div className="space-y-2 text-sm">
                <label className="flex items-center gap-2">
                  <input type="checkbox" className="rounded" />
                  Keyboard navigation works
                </label>
                <label className="flex items-center gap-2">
                  <input type="checkbox" className="rounded" />
                  Screen readers can navigate
                </label>
                <label className="flex items-center gap-2">
                  <input type="checkbox" className="rounded" />
                  ARIA labels are present
                </label>
                <label className="flex items-center gap-2">
                  <input type="checkbox" className="rounded" />
                  Focus indicators are visible
                </label>
              </div>
            </div>

            <div>
              <h4 className="font-medium mb-3 flex items-center gap-2">
                <Zap className="w-4 h-4" />
                Performance
              </h4>
              <div className="space-y-2 text-sm">
                <label className="flex items-center gap-2">
                  <input type="checkbox" className="rounded" />
                  Bundle size is optimized
                </label>
                <label className="flex items-center gap-2">
                  <input type="checkbox" className="rounded" />
                  AI components are lazy-loaded
                </label>
                <label className="flex items-center gap-2">
                  <input type="checkbox" className="rounded" />
                  Dashboard loads quickly
                </label>
                <label className="flex items-center gap-2">
                  <input type="checkbox" className="rounded" />
                  No N+1 query issues
                </label>
              </div>
            </div>

            <div>
              <h4 className="font-medium mb-3 flex items-center gap-2">
                <Settings className="w-4 h-4" />
                Configuration
              </h4>
              <div className="space-y-2 text-sm">
                <label className="flex items-center gap-2">
                  <input type="checkbox" className="rounded" />
                  Environment variables are set
                </label>
                <label className="flex items-center gap-2">
                  <input type="checkbox" className="rounded" />
                  API keys are working
                </label>
                <label className="flex items-center gap-2">
                  <input type="checkbox" className="rounded" />
                  Database connections work
                </label>
                <label className="flex items-center gap-2">
                  <input type="checkbox" className="rounded" />
                  Profile data is clean
                </label>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* External Resources */}
      <Card>
        <CardHeader>
          <CardTitle>External Testing Resources</CardTitle>
          <CardDescription>
            Additional tools and resources for comprehensive testing
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <h4 className="font-medium">Browser DevTools</h4>
              <ul className="text-sm space-y-1 text-gray-600">
                <li>• Lighthouse (Performance, Accessibility, SEO)</li>
                <li>• Network tab (Bundle analysis)</li>
                <li>• Console (Error monitoring)</li>
                <li>• Accessibility inspector</li>
              </ul>
            </div>
            
            <div className="space-y-2">
              <h4 className="font-medium">Screen Readers</h4>
              <ul className="text-sm space-y-1 text-gray-600">
                <li>• NVDA (Windows, Free)</li>
                <li>• JAWS (Windows, Commercial)</li>
                <li>• VoiceOver (Mac, Built-in)</li>
                <li>• ORCA (Linux, Free)</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
