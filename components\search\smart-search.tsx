'use client'

import React, { useState, useRef, useEffect } from 'react'
import { Search, Clock, TrendingUp, X, ArrowRight } from 'lucide-react'
import { cn } from '@/lib/utils'

interface SearchResult {
  id: string
  title: string
  description?: string
  category: 'transaction' | 'goal' | 'page' | 'contact'
  href: string
  icon?: React.ReactNode
  metadata?: string
}

interface SmartSearchProps {
  placeholder?: string
  className?: string
  onSearch?: (query: string) => void
  onSelect?: (result: SearchResult) => void
}

export function SmartSearch({ 
  placeholder = "Search transactions, goals, or navigate...", 
  className,
  onSearch,
  onSelect 
}: SmartSearchProps) {
  const [query, setQuery] = useState('')
  const [isOpen, setIsOpen] = useState(false)
  const [results, setResults] = useState<SearchResult[]>([])
  const [recentSearches, setRecentSearches] = useState<string[]>([])
  const [selectedIndex, setSelectedIndex] = useState(-1)
  
  const inputRef = useRef<HTMLInputElement>(null)
  const resultsRef = useRef<HTMLDivElement>(null)
  
  // Mock search results - in real app, this would be an API call
  const mockResults: SearchResult[] = [
    {
      id: '1',
      title: 'Starbucks Coffee',
      description: 'Transaction from yesterday',
      category: 'transaction',
      href: '/transactions/1',
      metadata: '$4.50 • Food & Dining'
    },
    {
      id: '2',
      title: 'Emergency Fund Goal',
      description: '75% complete',
      category: 'goal',
      href: '/goals/emergency-fund',
      metadata: '$7,500 of $10,000'
    },
    {
      id: '3',
      title: 'AI Assistant',
      description: 'Get financial insights',
      category: 'page',
      href: '/ai-chat',
      metadata: 'Navigate to page'
    }
  ]
  
  // Load recent searches from localStorage
  useEffect(() => {
    const saved = localStorage.getItem('recent-searches')
    if (saved) {
      setRecentSearches(JSON.parse(saved))
    }
  }, [])
  
  // Search functionality
  useEffect(() => {
    if (query.length > 0) {
      // Simulate API search with debouncing
      const timeoutId = setTimeout(() => {
        const filtered = mockResults.filter(result =>
          result.title.toLowerCase().includes(query.toLowerCase()) ||
          result.description?.toLowerCase().includes(query.toLowerCase())
        )
        setResults(filtered)
        setSelectedIndex(-1)
      }, 150)
      
      return () => clearTimeout(timeoutId)
    } else {
      setResults([])
      setSelectedIndex(-1)
    }
  }, [query])
  
  // Keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!isOpen) return
    
    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault()
        setSelectedIndex(prev => 
          prev < results.length - 1 ? prev + 1 : prev
        )
        break
      case 'ArrowUp':
        e.preventDefault()
        setSelectedIndex(prev => prev > 0 ? prev - 1 : -1)
        break
      case 'Enter':
        e.preventDefault()
        if (selectedIndex >= 0 && results[selectedIndex]) {
          handleSelect(results[selectedIndex])
        } else if (query) {
          handleSearch()
        }
        break
      case 'Escape':
        setIsOpen(false)
        inputRef.current?.blur()
        break
    }
  }
  
  const handleSearch = () => {
    if (query.trim()) {
      // Add to recent searches
      const newRecent = [query, ...recentSearches.filter(s => s !== query)].slice(0, 5)
      setRecentSearches(newRecent)
      localStorage.setItem('recent-searches', JSON.stringify(newRecent))
      
      onSearch?.(query)
      setIsOpen(false)
    }
  }
  
  const handleSelect = (result: SearchResult) => {
    onSelect?.(result)
    setQuery('')
    setIsOpen(false)
    
    // Add to recent searches
    const newRecent = [result.title, ...recentSearches.filter(s => s !== result.title)].slice(0, 5)
    setRecentSearches(newRecent)
    localStorage.setItem('recent-searches', JSON.stringify(newRecent))
  }
  
  const handleRecentSearch = (search: string) => {
    setQuery(search)
    onSearch?.(search)
    setIsOpen(false)
  }
  
  const clearRecentSearches = () => {
    setRecentSearches([])
    localStorage.removeItem('recent-searches')
  }
  
  const getCategoryIcon = (category: SearchResult['category']) => {
    switch (category) {
      case 'transaction': return '💳'
      case 'goal': return '🎯'
      case 'page': return '📄'
      case 'contact': return '👤'
      default: return '🔍'
    }
  }
  
  return (
    <div className={cn("relative w-full max-w-md", className)}>
      {/* Search Input */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-text-tertiary" />
        <input
          ref={inputRef}
          type="text"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          onFocus={() => setIsOpen(true)}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          className="w-full pl-10 pr-4 py-2 border border-border-default rounded-lg bg-surface text-text-primary placeholder:text-text-tertiary focus:outline-none focus:border-primary focus:ring-2 focus:ring-primary/20 transition-all duration-200"
          aria-label="Search"
          aria-expanded={isOpen}
          aria-haspopup="listbox"
          role="combobox"
        />
      </div>
      
      {/* Search Results Dropdown */}
      {isOpen && (
        <div 
          ref={resultsRef}
          className="absolute top-full left-0 right-0 mt-2 bg-surface border border-border-default rounded-lg shadow-lg z-50 max-h-96 overflow-y-auto"
          role="listbox"
        >
          {/* Recent Searches */}
          {query === '' && recentSearches.length > 0 && (
            <div className="p-3 border-b border-border-subtle">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-text-secondary flex items-center gap-2">
                  <Clock className="w-4 h-4" />
                  Recent Searches
                </span>
                <button
                  onClick={clearRecentSearches}
                  className="text-xs text-text-tertiary hover:text-text-secondary transition-colors"
                >
                  Clear
                </button>
              </div>
              <div className="space-y-1">
                {recentSearches.map((search, index) => (
                  <button
                    key={index}
                    onClick={() => handleRecentSearch(search)}
                    className="w-full text-left px-2 py-1 text-sm text-text-secondary hover:bg-neutral-100 rounded transition-colors"
                  >
                    {search}
                  </button>
                ))}
              </div>
            </div>
          )}
          
          {/* Search Results */}
          {results.length > 0 && (
            <div className="p-2">
              {results.map((result, index) => (
                <button
                  key={result.id}
                  onClick={() => handleSelect(result)}
                  className={cn(
                    "w-full text-left p-3 rounded-lg transition-colors flex items-start gap-3 group",
                    selectedIndex === index 
                      ? "bg-primary/10 border border-primary/20" 
                      : "hover:bg-neutral-100"
                  )}
                  role="option"
                  aria-selected={selectedIndex === index}
                >
                  <span className="text-lg flex-shrink-0 mt-0.5">
                    {getCategoryIcon(result.category)}
                  </span>
                  <div className="flex-1 min-w-0">
                    <div className="font-medium text-text-primary group-hover:text-primary transition-colors">
                      {result.title}
                    </div>
                    {result.description && (
                      <div className="text-sm text-text-secondary mt-0.5">
                        {result.description}
                      </div>
                    )}
                    {result.metadata && (
                      <div className="text-xs text-text-tertiary mt-1">
                        {result.metadata}
                      </div>
                    )}
                  </div>
                  <ArrowRight className="w-4 h-4 text-text-tertiary group-hover:text-primary transition-colors flex-shrink-0 mt-1" />
                </button>
              ))}
            </div>
          )}
          
          {/* No Results */}
          {query && results.length === 0 && (
            <div className="p-6 text-center text-text-secondary">
              <Search className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">No results found for "{query}"</p>
              <button
                onClick={handleSearch}
                className="mt-2 text-sm text-primary hover:underline"
              >
                Search anyway
              </button>
            </div>
          )}
          
          {/* Quick Actions */}
          {query === '' && (
            <div className="p-3 border-t border-border-subtle">
              <div className="text-sm font-medium text-text-secondary mb-2 flex items-center gap-2">
                <TrendingUp className="w-4 h-4" />
                Quick Actions
              </div>
              <div className="space-y-1">
                <button className="w-full text-left px-2 py-1 text-sm text-text-secondary hover:bg-neutral-100 rounded transition-colors">
                  Add new transaction
                </button>
                <button className="w-full text-left px-2 py-1 text-sm text-text-secondary hover:bg-neutral-100 rounded transition-colors">
                  Create new goal
                </button>
                <button className="w-full text-left px-2 py-1 text-sm text-text-secondary hover:bg-neutral-100 rounded transition-colors">
                  View spending insights
                </button>
              </div>
            </div>
          )}
        </div>
      )}
      
      {/* Backdrop */}
      {isOpen && (
        <div 
          className="fixed inset-0 z-40" 
          onClick={() => setIsOpen(false)}
          aria-hidden="true"
        />
      )}
    </div>
  )
}
