'use client'

import React from 'react'
import { cn } from '@/lib/utils'

// Brand Color Palette
const brandColors = {
  primary: '#22C55E',
  primaryLight: '#4ADE80',
  primaryDark: '#16A34A',
  secondary: '#3B82F6',
  accent: '#F59E0B',
  success: '#10B981',
  warning: '#F59E0B',
  error: '#EF4444',
  neutral: '#6B7280'
}

// Custom Brand Loading Animation
export function BrandLoadingAnimation({ size = 60, className }: { size?: number; className?: string }) {
  return (
    <div className={cn("flex items-center justify-center", className)}>
      <svg
        width={size}
        height={size}
        viewBox="0 0 100 100"
        className="animate-spin"
      >
        {/* Outer Ring */}
        <circle
          cx="50"
          cy="50"
          r="45"
          fill="none"
          stroke={brandColors.primary}
          strokeWidth="3"
          strokeLinecap="round"
          strokeDasharray="70 30"
          className="animate-pulse"
        />
        
        {/* Inner Ring */}
        <circle
          cx="50"
          cy="50"
          r="30"
          fill="none"
          stroke={brandColors.secondary}
          strokeWidth="2"
          strokeLinecap="round"
          strokeDasharray="50 20"
          className="animate-spin"
          style={{ animationDirection: 'reverse', animationDuration: '1.5s' }}
        />
        
        {/* Center Dollar Sign */}
        <text
          x="50"
          y="58"
          textAnchor="middle"
          fontSize="24"
          fontWeight="bold"
          fill={brandColors.primary}
          className="animate-pulse"
        >
          $
        </text>
      </svg>
    </div>
  )
}

// Empty State Illustration - No Transactions
export function NoTransactionsIllustration({ className }: { className?: string }) {
  return (
    <div className={cn("flex items-center justify-center", className)}>
      <svg
        width="200"
        height="160"
        viewBox="0 0 200 160"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        {/* Background Circle */}
        <circle
          cx="100"
          cy="80"
          r="70"
          fill={brandColors.primary}
          fillOpacity="0.1"
          className="animate-pulse"
        />
        
        {/* Wallet */}
        <rect
          x="60"
          y="60"
          width="80"
          height="50"
          rx="8"
          fill={brandColors.primary}
          fillOpacity="0.8"
        />
        <rect
          x="60"
          y="60"
          width="80"
          height="15"
          rx="8"
          fill={brandColors.primaryDark}
        />
        
        {/* Empty Indicator */}
        <circle
          cx="100"
          cy="85"
          r="15"
          fill="none"
          stroke={brandColors.neutral}
          strokeWidth="2"
          strokeDasharray="5 5"
          className="animate-pulse"
        />
        
        {/* Floating Coins */}
        <circle
          cx="140"
          cy="45"
          r="8"
          fill={brandColors.accent}
          className="float-animation"
          style={{ animationDelay: '0s' }}
        />
        <circle
          cx="160"
          cy="55"
          r="6"
          fill={brandColors.secondary}
          className="float-animation"
          style={{ animationDelay: '0.5s' }}
        />
        <circle
          cx="50"
          cy="40"
          r="7"
          fill={brandColors.success}
          className="float-animation"
          style={{ animationDelay: '1s' }}
        />
        
        {/* Dollar Signs */}
        <text x="140" y="50" textAnchor="middle" fontSize="10" fontWeight="bold" fill="white">$</text>
        <text x="160" y="59" textAnchor="middle" fontSize="8" fontWeight="bold" fill="white">$</text>
        <text x="50" y="45" textAnchor="middle" fontSize="9" fontWeight="bold" fill="white">$</text>
      </svg>
    </div>
  )
}

// Empty State Illustration - No Goals
export function NoGoalsIllustration({ className }: { className?: string }) {
  return (
    <div className={cn("flex items-center justify-center", className)}>
      <svg
        width="200"
        height="160"
        viewBox="0 0 200 160"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        {/* Background */}
        <ellipse
          cx="100"
          cy="140"
          rx="80"
          ry="15"
          fill={brandColors.neutral}
          fillOpacity="0.1"
        />
        
        {/* Target Base */}
        <circle
          cx="100"
          cy="80"
          r="50"
          fill="none"
          stroke={brandColors.primary}
          strokeWidth="3"
          strokeOpacity="0.3"
        />
        <circle
          cx="100"
          cy="80"
          r="35"
          fill="none"
          stroke={brandColors.primary}
          strokeWidth="3"
          strokeOpacity="0.5"
        />
        <circle
          cx="100"
          cy="80"
          r="20"
          fill="none"
          stroke={brandColors.primary}
          strokeWidth="3"
          strokeOpacity="0.7"
        />
        <circle
          cx="100"
          cy="80"
          r="8"
          fill={brandColors.primary}
          className="animate-pulse"
        />
        
        {/* Arrow */}
        <path
          d="M70 50 L100 80 L75 85 L100 80 L95 55"
          stroke={brandColors.secondary}
          strokeWidth="3"
          strokeLinecap="round"
          strokeLinejoin="round"
          fill="none"
          className="float-animation"
        />
        
        {/* Stars */}
        <g className="animate-pulse">
          <path
            d="M140 30 L142 36 L148 36 L143 40 L145 46 L140 42 L135 46 L137 40 L132 36 L138 36 Z"
            fill={brandColors.accent}
          />
          <path
            d="M160 70 L161 74 L165 74 L162 77 L163 81 L160 78 L157 81 L158 77 L155 74 L159 74 Z"
            fill={brandColors.success}
          />
          <path
            d="M50 35 L51 39 L55 39 L52 42 L53 46 L50 43 L47 46 L48 42 L45 39 L49 39 Z"
            fill={brandColors.error}
          />
        </g>
      </svg>
    </div>
  )
}

// Success Celebration Illustration
export function SuccessCelebrationIllustration({ className }: { className?: string }) {
  return (
    <div className={cn("flex items-center justify-center", className)}>
      <svg
        width="200"
        height="160"
        viewBox="0 0 200 160"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        {/* Confetti */}
        <g className="animate-bounce">
          <rect x="40" y="20" width="4" height="4" fill={brandColors.primary} rx="1" />
          <rect x="160" y="30" width="4" height="4" fill={brandColors.secondary} rx="1" />
          <rect x="30" y="50" width="4" height="4" fill={brandColors.accent} rx="1" />
          <rect x="170" y="60" width="4" height="4" fill={brandColors.success} rx="1" />
          <rect x="50" y="25" width="4" height="4" fill={brandColors.error} rx="1" />
          <rect x="150" y="45" width="4" height="4" fill={brandColors.warning} rx="1" />
        </g>
        
        {/* Trophy */}
        <g transform="translate(100, 80)">
          {/* Base */}
          <rect x="-20" y="40" width="40" height="8" rx="4" fill={brandColors.accent} />
          <rect x="-15" y="35" width="30" height="8" rx="4" fill={brandColors.accent} />
          
          {/* Stem */}
          <rect x="-3" y="25" width="6" height="15" fill={brandColors.accent} />
          
          {/* Cup */}
          <path
            d="M-25 -20 Q-25 -35 -15 -35 L15 -35 Q25 -35 25 -20 L20 20 Q20 25 15 25 L-15 25 Q-20 25 -20 20 Z"
            fill={brandColors.primary}
            className="animate-pulse"
          />
          
          {/* Handles */}
          <path
            d="M-25 -10 Q-35 -10 -35 0 Q-35 10 -25 10"
            stroke={brandColors.primaryDark}
            strokeWidth="3"
            fill="none"
          />
          <path
            d="M25 -10 Q35 -10 35 0 Q35 10 25 10"
            stroke={brandColors.primaryDark}
            strokeWidth="3"
            fill="none"
          />
          
          {/* Star */}
          <path
            d="M0 -15 L2 -9 L8 -9 L3 -5 L5 1 L0 -3 L-5 1 L-3 -5 L-8 -9 L-2 -9 Z"
            fill={brandColors.accent}
            className="animate-spin"
            style={{ animationDuration: '3s' }}
          />
        </g>
        
        {/* Sparkles */}
        <g className="animate-pulse">
          <circle cx="60" cy="40" r="2" fill={brandColors.accent} />
          <circle cx="140" cy="50" r="2" fill={brandColors.success} />
          <circle cx="70" cy="120" r="2" fill={brandColors.secondary} />
          <circle cx="130" cy="110" r="2" fill={brandColors.primary} />
        </g>
      </svg>
    </div>
  )
}

// Error State Illustration
export function ErrorStateIllustration({ className }: { className?: string }) {
  return (
    <div className={cn("flex items-center justify-center", className)}>
      <svg
        width="200"
        height="160"
        viewBox="0 0 200 160"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        {/* Background Circle */}
        <circle
          cx="100"
          cy="80"
          r="60"
          fill={brandColors.error}
          fillOpacity="0.1"
          className="animate-pulse"
        />
        
        {/* Warning Triangle */}
        <path
          d="M100 30 L140 110 L60 110 Z"
          fill={brandColors.error}
          fillOpacity="0.8"
        />
        
        {/* Exclamation Mark */}
        <rect
          x="97"
          y="50"
          width="6"
          height="35"
          rx="3"
          fill="white"
        />
        <circle
          cx="100"
          cy="95"
          r="4"
          fill="white"
        />
        
        {/* Floating Elements */}
        <g className="float-animation">
          <circle cx="70" cy="50" r="3" fill={brandColors.error} fillOpacity="0.6" />
          <circle cx="130" cy="60" r="3" fill={brandColors.error} fillOpacity="0.6" />
          <circle cx="80" cy="120" r="3" fill={brandColors.error} fillOpacity="0.6" />
          <circle cx="120" cy="115" r="3" fill={brandColors.error} fillOpacity="0.6" />
        </g>
      </svg>
    </div>
  )
}

// Brand Mascot - Friendly Finance Helper
export function BrandMascot({ mood = 'happy', size = 100, className }: { 
  mood?: 'happy' | 'thinking' | 'celebrating' | 'sleeping'
  size?: number
  className?: string 
}) {
  return (
    <div className={cn("flex items-center justify-center", className)}>
      <svg
        width={size}
        height={size}
        viewBox="0 0 100 100"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        {/* Body (Coin) */}
        <circle
          cx="50"
          cy="50"
          r="35"
          fill={brandColors.primary}
          stroke={brandColors.primaryDark}
          strokeWidth="3"
        />
        
        {/* Inner Circle */}
        <circle
          cx="50"
          cy="50"
          r="25"
          fill="none"
          stroke={brandColors.primaryDark}
          strokeWidth="2"
          strokeOpacity="0.3"
        />
        
        {/* Dollar Sign */}
        <text
          x="50"
          y="58"
          textAnchor="middle"
          fontSize="20"
          fontWeight="bold"
          fill={brandColors.primaryDark}
        >
          $
        </text>
        
        {/* Eyes */}
        {mood === 'happy' && (
          <>
            <circle cx="42" cy="35" r="3" fill={brandColors.primaryDark} />
            <circle cx="58" cy="35" r="3" fill={brandColors.primaryDark} />
          </>
        )}
        
        {mood === 'thinking' && (
          <>
            <circle cx="42" cy="35" r="3" fill={brandColors.primaryDark} />
            <path d="M55 32 Q60 35 55 38" stroke={brandColors.primaryDark} strokeWidth="2" fill="none" />
          </>
        )}
        
        {mood === 'celebrating' && (
          <>
            <path d="M39 32 L45 38 M45 32 L39 38" stroke={brandColors.primaryDark} strokeWidth="2" />
            <path d="M55 32 L61 38 M61 32 L55 38" stroke={brandColors.primaryDark} strokeWidth="2" />
          </>
        )}
        
        {mood === 'sleeping' && (
          <>
            <path d="M39 35 Q45 32 51 35" stroke={brandColors.primaryDark} strokeWidth="2" fill="none" />
            <path d="M55 35 Q61 32 67 35" stroke={brandColors.primaryDark} strokeWidth="2" fill="none" />
          </>
        )}
        
        {/* Mouth */}
        {mood === 'happy' && (
          <path
            d="M42 65 Q50 72 58 65"
            stroke={brandColors.primaryDark}
            strokeWidth="2"
            fill="none"
            strokeLinecap="round"
          />
        )}
        
        {mood === 'thinking' && (
          <circle cx="50" cy="68" r="2" fill={brandColors.primaryDark} />
        )}
        
        {mood === 'celebrating' && (
          <ellipse cx="50" cy="68" rx="8" ry="5" fill={brandColors.primaryDark} />
        )}
        
        {mood === 'sleeping' && (
          <path
            d="M45 68 Q50 65 55 68"
            stroke={brandColors.primaryDark}
            strokeWidth="2"
            fill="none"
            strokeLinecap="round"
          />
        )}
        
        {/* Thought Bubble (for thinking mood) */}
        {mood === 'thinking' && (
          <g className="animate-pulse">
            <circle cx="70" cy="25" r="8" fill="white" stroke={brandColors.primaryDark} strokeWidth="1" />
            <circle cx="65" cy="30" r="3" fill="white" stroke={brandColors.primaryDark} strokeWidth="1" />
            <circle cx="62" cy="33" r="2" fill="white" stroke={brandColors.primaryDark} strokeWidth="1" />
            <text x="70" y="29" textAnchor="middle" fontSize="8" fill={brandColors.primaryDark}>?</text>
          </g>
        )}
        
        {/* Celebration Sparkles */}
        {mood === 'celebrating' && (
          <g className="animate-spin" style={{ animationDuration: '3s' }}>
            <path d="M25 25 L27 30 L32 30 L28 33 L30 38 L25 35 L20 38 L22 33 L18 30 L23 30 Z" fill={brandColors.accent} />
            <path d="M75 20 L76 23 L79 23 L77 25 L78 28 L75 26 L72 28 L73 25 L71 23 L74 23 Z" fill={brandColors.secondary} />
          </g>
        )}
        
        {/* Sleep Z's */}
        {mood === 'sleeping' && (
          <g className="animate-pulse">
            <text x="70" y="25" fontSize="12" fill={brandColors.primaryDark} fillOpacity="0.7">Z</text>
            <text x="75" y="20" fontSize="10" fill={brandColors.primaryDark} fillOpacity="0.5">z</text>
            <text x="78" y="15" fontSize="8" fill={brandColors.primaryDark} fillOpacity="0.3">z</text>
          </g>
        )}
      </svg>
    </div>
  )
}

// Custom Icon Components
export function BrandIcon({ name, size = 24, className }: { 
  name: 'wallet' | 'chart' | 'goal' | 'transaction' | 'insight'
  size?: number
  className?: string 
}) {
  const iconProps = {
    width: size,
    height: size,
    viewBox: "0 0 24 24",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    className: cn("text-current", className)
  }
  
  switch (name) {
    case 'wallet':
      return (
        <svg {...iconProps}>
          <path
            d="M19 7V6a1 1 0 0 0-1-1H5a2 2 0 0 0 0 4h15a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1v-2a2 2 0 0 0-2-2H2"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M3 5v14a2 2 0 0 0 2 2h15a1 1 0 0 0 1-1v-4"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      )
    
    case 'chart':
      return (
        <svg {...iconProps}>
          <path
            d="M3 3v18h18"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M18.7 8l-5.1 5.2-2.8-2.7L7 14.3"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      )
    
    default:
      return null
  }
}
