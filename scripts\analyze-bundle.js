#!/usr/bin/env node

/**
 * Bundle Size Analysis Script
 * Analyzes the Next.js build output to provide insights into bundle size
 */

const fs = require('fs')
const path = require('path')

function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

function analyzeDirectory(dirPath, extensions = ['.js', '.css']) {
  const results = {
    totalSize: 0,
    files: [],
    byExtension: {}
  }

  if (!fs.existsSync(dirPath)) {
    console.log(`Directory ${dirPath} does not exist`)
    return results
  }

  function walkDirectory(currentPath) {
    const items = fs.readdirSync(currentPath)
    
    for (const item of items) {
      const itemPath = path.join(currentPath, item)
      const stat = fs.statSync(itemPath)
      
      if (stat.isDirectory()) {
        walkDirectory(itemPath)
      } else if (stat.isFile()) {
        const ext = path.extname(item)
        if (extensions.includes(ext)) {
          const size = stat.size
          const relativePath = path.relative(dirPath, itemPath)
          
          results.totalSize += size
          results.files.push({
            path: relativePath,
            size: size,
            formattedSize: formatBytes(size),
            extension: ext
          })
          
          if (!results.byExtension[ext]) {
            results.byExtension[ext] = { count: 0, size: 0 }
          }
          results.byExtension[ext].count++
          results.byExtension[ext].size += size
        }
      }
    }
  }

  walkDirectory(dirPath)
  
  // Sort files by size (largest first)
  results.files.sort((a, b) => b.size - a.size)
  
  return results
}

function analyzeBuild() {
  console.log('🔍 Analyzing Next.js Build Output...\n')
  
  const buildPath = path.join(process.cwd(), '.next')
  
  if (!fs.existsSync(buildPath)) {
    console.log('❌ No build found. Please run "npm run build" first.')
    return
  }

  // Analyze static files
  const staticPath = path.join(buildPath, 'static')
  const staticAnalysis = analyzeDirectory(staticPath, ['.js', '.css'])
  
  console.log('📊 Bundle Size Analysis')
  console.log('=' .repeat(50))
  console.log(`Total Static Assets: ${formatBytes(staticAnalysis.totalSize)}`)
  console.log()

  // Breakdown by file type
  console.log('📁 Breakdown by File Type:')
  for (const [ext, data] of Object.entries(staticAnalysis.byExtension)) {
    console.log(`  ${ext}: ${formatBytes(data.size)} (${data.count} files)`)
  }
  console.log()

  // Largest files
  console.log('📈 Largest Files (Top 10):')
  staticAnalysis.files.slice(0, 10).forEach((file, index) => {
    console.log(`  ${index + 1}. ${file.path} - ${file.formattedSize}`)
  })
  console.log()

  // AI-related files analysis
  console.log('🤖 AI Components Analysis:')
  const aiFiles = staticAnalysis.files.filter(file => 
    file.path.includes('ai-chat') || 
    file.path.includes('ai-sdk') ||
    file.path.includes('google')
  )
  
  if (aiFiles.length > 0) {
    const aiTotalSize = aiFiles.reduce((sum, file) => sum + file.size, 0)
    console.log(`  AI-related files: ${formatBytes(aiTotalSize)} (${aiFiles.length} files)`)
    console.log(`  Percentage of total: ${((aiTotalSize / staticAnalysis.totalSize) * 100).toFixed(2)}%`)
    
    if (aiTotalSize > staticAnalysis.totalSize * 0.1) {
      console.log('  ⚠️  AI components are taking up significant space - ensure lazy loading is working')
    } else {
      console.log('  ✅ AI components are properly optimized')
    }
  } else {
    console.log('  ✅ No AI components found in main bundle (likely lazy-loaded)')
  }
  console.log()

  // Performance recommendations
  console.log('💡 Performance Recommendations:')
  
  const jsSize = staticAnalysis.byExtension['.js']?.size || 0
  const cssSize = staticAnalysis.byExtension['.css']?.size || 0
  
  if (jsSize > 1024 * 1024) { // 1MB
    console.log('  ⚠️  JavaScript bundle is large (>1MB) - consider code splitting')
  } else {
    console.log('  ✅ JavaScript bundle size is reasonable')
  }
  
  if (cssSize > 200 * 1024) { // 200KB
    console.log('  ⚠️  CSS bundle is large (>200KB) - consider CSS optimization')
  } else {
    console.log('  ✅ CSS bundle size is reasonable')
  }

  // Check for duplicate dependencies
  const duplicateCheck = staticAnalysis.files.filter(file => 
    file.path.includes('node_modules') || file.path.includes('vendor')
  )
  
  if (duplicateCheck.length > 0) {
    console.log('  ⚠️  Potential duplicate dependencies detected')
  } else {
    console.log('  ✅ No obvious duplicate dependencies')
  }

  console.log()
  console.log('🎯 Overall Assessment:')
  
  const totalMB = staticAnalysis.totalSize / (1024 * 1024)
  if (totalMB < 1) {
    console.log('  ✅ Excellent - Bundle size is under 1MB')
  } else if (totalMB < 2) {
    console.log('  ✅ Good - Bundle size is reasonable (1-2MB)')
  } else if (totalMB < 5) {
    console.log('  ⚠️  Fair - Bundle size is getting large (2-5MB)')
  } else {
    console.log('  ❌ Poor - Bundle size is very large (>5MB)')
  }
}

function generateReport() {
  const buildPath = path.join(process.cwd(), '.next')
  const staticPath = path.join(buildPath, 'static')
  const staticAnalysis = analyzeDirectory(staticPath, ['.js', '.css'])
  
  const report = {
    timestamp: new Date().toISOString(),
    totalSize: staticAnalysis.totalSize,
    formattedTotalSize: formatBytes(staticAnalysis.totalSize),
    breakdown: {},
    largestFiles: staticAnalysis.files.slice(0, 10),
    recommendations: []
  }
  
  // Add breakdown
  for (const [ext, data] of Object.entries(staticAnalysis.byExtension)) {
    report.breakdown[ext] = {
      size: data.size,
      formattedSize: formatBytes(data.size),
      count: data.count,
      percentage: ((data.size / staticAnalysis.totalSize) * 100).toFixed(2)
    }
  }
  
  // Add recommendations
  const jsSize = staticAnalysis.byExtension['.js']?.size || 0
  const totalMB = staticAnalysis.totalSize / (1024 * 1024)
  
  if (jsSize > 1024 * 1024) {
    report.recommendations.push('Consider code splitting for JavaScript bundle')
  }
  
  if (totalMB > 2) {
    report.recommendations.push('Bundle size is large - review dependencies')
  }
  
  // Save report
  const reportPath = path.join(process.cwd(), 'bundle-analysis-report.json')
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2))
  console.log(`📄 Report saved to: ${reportPath}`)
  
  return report
}

// Main execution
if (require.main === module) {
  const args = process.argv.slice(2)
  
  if (args.includes('--json')) {
    generateReport()
  } else {
    analyzeBuild()
  }
}

module.exports = { analyzeBuild, generateReport, analyzeDirectory }
