'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/lib/contexts/auth-context'
import { getUserRecurringPayments, deleteRecurringPayment, updateRecurringPayment } from '@/lib/supabase/queries'
import { RecurringPayment } from '@/lib/types/database'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu'
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { 
  Search, 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  Plus,
  Play,
  Pause,
  Calendar,
  DollarSign
} from 'lucide-react'
import { format, isAfter, isBefore, addDays } from 'date-fns'

interface RecurringPaymentsTableProps {
  onEditPayment: (payment: RecurringPayment) => void
  onAddPayment: () => void
  refreshTrigger?: number
}

export function RecurringPaymentsTable({ onEditPayment, onAddPayment, refreshTrigger }: RecurringPaymentsTableProps) {
  const { user, profile } = useAuth()
  const [payments, setPayments] = useState<RecurringPayment[]>([])
  const [filteredPayments, setFilteredPayments] = useState<RecurringPayment[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [paymentToDelete, setPaymentToDelete] = useState<RecurringPayment | null>(null)
  
  // Filter states
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'paused'>('all')
  const [frequencyFilter, setFrequencyFilter] = useState('all')

  useEffect(() => {
    loadPayments()
  }, [user, refreshTrigger])

  useEffect(() => {
    applyFilters()
  }, [payments, searchTerm, statusFilter, frequencyFilter])

  const loadPayments = async () => {
    if (!user) return
    
    setLoading(true)
    try {
      const userPayments = await getUserRecurringPayments(user.id)
      setPayments(userPayments)
    } catch (err) {
      console.error('Error loading recurring payments:', err)
      setError('Failed to load recurring payments')
    } finally {
      setLoading(false)
    }
  }

  const applyFilters = () => {
    let filtered = [...payments]

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(payment =>
        payment.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (payment.category && typeof payment.category === 'string' && payment.category.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (payment.description && payment.description.toLowerCase().includes(searchTerm.toLowerCase()))
      )
    }

    // Status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(payment => 
        statusFilter === 'active' ? payment.is_active : !payment.is_active
      )
    }

    // Frequency filter
    if (frequencyFilter !== 'all') {
      filtered = filtered.filter(payment => payment.frequency === frequencyFilter)
    }

    // Sort by next payment date
    filtered.sort((a, b) => new Date(a.next_payment_date || a.start_date).getTime() - new Date(b.next_payment_date || b.start_date).getTime())

    setFilteredPayments(filtered)
  }

  const handleDeletePayment = async (payment: RecurringPayment) => {
    try {
      await deleteRecurringPayment(payment.id)
      await loadPayments()
      setDeleteDialogOpen(false)
      setPaymentToDelete(null)
    } catch (err) {
      console.error('Error deleting recurring payment:', err)
      setError('Failed to delete recurring payment')
    }
  }

  const handleToggleStatus = async (payment: RecurringPayment) => {
    try {
      await updateRecurringPayment(payment.id, { is_active: !payment.is_active })
      await loadPayments()
    } catch (err) {
      console.error('Error updating payment status:', err)
      setError('Failed to update payment status')
    }
  }

  const formatAmount = (amount: number) => {
    const currency = profile?.currency_code || 'USD'
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount)
  }

  const getPaymentStatus = (payment: RecurringPayment) => {
    if (!payment.is_active) return 'paused'
    
    const nextPayment = new Date(payment.next_payment_date)
    const today = new Date()
    const tomorrow = addDays(today, 1)
    
    if (isBefore(nextPayment, tomorrow)) return 'due'
    return 'active'
  }

  const getStatusBadge = (payment: RecurringPayment) => {
    const status = getPaymentStatus(payment)
    
    switch (status) {
      case 'due':
        return <Badge variant="destructive">Due Soon</Badge>
      case 'active':
        return <Badge variant="default">Active</Badge>
      case 'paused':
        return <Badge variant="secondary">Paused</Badge>
      default:
        return <Badge variant="outline">Unknown</Badge>
    }
  }

  const getUniqueFrequencies = () => {
    const frequencies = [...new Set(payments.map(p => p.frequency))]
    return frequencies.sort()
  }

  const calculateMonthlyTotal = () => {
    return filteredPayments
      .filter(p => p.is_active)
      .reduce((total, payment) => {
        // Convert all frequencies to monthly equivalent
        let monthlyAmount = payment.amount
        switch (payment.frequency) {
          case 'daily':
            monthlyAmount = payment.amount * 30
            break
          case 'weekly':
            monthlyAmount = payment.amount * 4.33
            break
          case 'yearly':
            monthlyAmount = payment.amount / 12
            break
          // monthly stays the same
        }
        return total + monthlyAmount
      }, 0)
  }

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading recurring payments...</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-4">
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Summary Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="w-5 h-5" />
            Monthly Commitments
          </CardTitle>
          <CardDescription>
            Total estimated monthly recurring payments
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-3xl font-bold text-primary">
            {formatAmount(calculateMonthlyTotal())}
          </div>
          <p className="text-sm text-muted-foreground">
            From {filteredPayments.filter(p => p.is_active).length} active payments
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Recurring Payments</CardTitle>
              <CardDescription>
                Manage your subscriptions and recurring expenses
              </CardDescription>
            </div>
            <Button onClick={onAddPayment}>
              <Plus className="w-4 h-4 mr-2" />
              Add Payment
            </Button>
          </div>
        </CardHeader>

        <CardContent>
          {/* Filters */}
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <Input
                  placeholder="Search payments..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <Select value={statusFilter} onValueChange={(value: any) => setStatusFilter(value)}>
              <SelectTrigger className="w-[140px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="paused">Paused</SelectItem>
              </SelectContent>
            </Select>

            <Select value={frequencyFilter} onValueChange={setFrequencyFilter}>
              <SelectTrigger className="w-[140px]">
                <SelectValue placeholder="Frequency" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Frequencies</SelectItem>
                {getUniqueFrequencies().map(frequency => (
                  <SelectItem key={frequency} value={frequency}>
                    {frequency.charAt(0).toUpperCase() + frequency.slice(1)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Table */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Payment</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Frequency</TableHead>
                  <TableHead>Next Payment</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Active</TableHead>
                  <TableHead className="w-12"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredPayments.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-8 text-muted-foreground">
                      No recurring payments found
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredPayments.map((payment) => (
                    <TableRow key={payment.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{payment.name}</div>
                          {payment.description && (
                            <div className="text-sm text-muted-foreground">
                              {payment.description}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">
                          {String(payment.category || 'Uncategorized')}
                        </Badge>
                      </TableCell>
                      <TableCell className="font-mono">
                        {formatAmount(payment.amount)}
                      </TableCell>
                      <TableCell>
                        <span className="capitalize">{payment.frequency}</span>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Calendar className="w-4 h-4 text-muted-foreground" />
                          {format(new Date(payment.next_payment_date || payment.start_date), 'MMM dd, yyyy')}
                        </div>
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(payment)}
                      </TableCell>
                      <TableCell>
                        <Switch
                          checked={payment.is_active}
                          onCheckedChange={() => handleToggleStatus(payment)}
                        />
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="w-4 h-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => onEditPayment(payment)}>
                              <Edit className="w-4 h-4 mr-2" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuItem 
                              onClick={() => handleToggleStatus(payment)}
                            >
                              {payment.is_active ? (
                                <>
                                  <Pause className="w-4 h-4 mr-2" />
                                  Pause
                                </>
                              ) : (
                                <>
                                  <Play className="w-4 h-4 mr-2" />
                                  Resume
                                </>
                              )}
                            </DropdownMenuItem>
                            <DropdownMenuItem 
                              onClick={() => {
                                setPaymentToDelete(payment)
                                setDeleteDialogOpen(true)
                              }}
                              className="text-red-600"
                            >
                              <Trash2 className="w-4 h-4 mr-2" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Recurring Payment</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete "{paymentToDelete?.name}"? 
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => paymentToDelete && handleDeletePayment(paymentToDelete)}
              className="bg-red-600 hover:bg-red-700"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
