import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const enhancedButtonVariants = cva(
  // Base styles using design system classes
  "btn-base inline-flex items-center justify-center gap-2 whitespace-nowrap font-semibold transition-all duration-150 ease-in-out disabled:pointer-events-none disabled:opacity-50 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 touch-manipulation select-none",
  {
    variants: {
      variant: {
        primary: "btn-primary bg-primary text-primary-foreground border-primary shadow-sm hover:bg-primary-hover hover:border-primary-hover hover:shadow-md active:bg-primary-active active:border-primary-active active:shadow-xs active:translate-y-px",
        
        secondary: "btn-secondary bg-transparent text-text-primary border border-border-default hover:bg-neutral-100 hover:border-border-strong active:bg-neutral-200 active:translate-y-px",
        
        ghost: "btn-ghost bg-transparent text-text-secondary border-transparent hover:bg-neutral-100 hover:text-text-primary active:bg-neutral-200",
        
        destructive: "btn-destructive bg-error text-error-foreground border-error shadow-sm hover:bg-error-700 hover:border-error-700 hover:shadow-md active:shadow-xs active:translate-y-px",
        
        success: "bg-success text-success-foreground border-success shadow-sm hover:bg-success-700 hover:border-success-700 hover:shadow-md active:shadow-xs active:translate-y-px",
        
        warning: "bg-warning text-warning-foreground border-warning shadow-sm hover:bg-warning-700 hover:border-warning-700 hover:shadow-md active:shadow-xs active:translate-y-px",
        
        info: "bg-info text-info-foreground border-info shadow-sm hover:bg-info-700 hover:border-info-700 hover:shadow-md active:shadow-xs active:translate-y-px",
        
        link: "text-primary underline-offset-4 hover:underline active:underline bg-transparent border-transparent p-0 h-auto",
      },
      size: {
        sm: "btn-sm h-8 px-3 text-sm rounded-md [&_svg]:w-4 [&_svg]:h-4",
        default: "btn-md h-10 px-4 text-sm rounded-md [&_svg]:w-5 [&_svg]:h-5",
        lg: "btn-lg h-12 px-6 text-base rounded-md [&_svg]:w-6 [&_svg]:h-6",
        icon: "h-10 w-10 p-0 rounded-md [&_svg]:w-5 [&_svg]:h-5",
        "icon-sm": "h-8 w-8 p-0 rounded-md [&_svg]:w-4 [&_svg]:h-4",
        "icon-lg": "h-12 w-12 p-0 rounded-md [&_svg]:w-6 [&_svg]:h-6",
      },
      loading: {
        true: "cursor-wait",
        false: "",
      },
    },
    defaultVariants: {
      variant: "primary",
      size: "default",
      loading: false,
    },
  }
)

export interface EnhancedButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof enhancedButtonVariants> {
  asChild?: boolean
  loading?: boolean
  loadingText?: string
  leftIcon?: React.ReactNode
  rightIcon?: React.ReactNode
}

const EnhancedButton = React.forwardRef<HTMLButtonElement, EnhancedButtonProps>(
  ({ 
    className, 
    variant, 
    size, 
    asChild = false, 
    loading = false,
    loadingText,
    leftIcon,
    rightIcon,
    children,
    disabled,
    ...props 
  }, ref) => {
    const Comp = asChild ? Slot : "button"
    
    const isDisabled = disabled || loading
    
    return (
      <Comp
        className={cn(enhancedButtonVariants({ variant, size, loading, className }))}
        ref={ref}
        disabled={isDisabled}
        aria-disabled={isDisabled}
        {...props}
      >
        {loading && (
          <svg
            className="animate-spin -ml-1 mr-2 h-4 w-4"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            aria-hidden="true"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            />
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            />
          </svg>
        )}
        
        {!loading && leftIcon && (
          <span className="flex-shrink-0" aria-hidden="true">
            {leftIcon}
          </span>
        )}
        
        <span className="truncate">
          {loading && loadingText ? loadingText : children}
        </span>
        
        {!loading && rightIcon && (
          <span className="flex-shrink-0" aria-hidden="true">
            {rightIcon}
          </span>
        )}
      </Comp>
    )
  }
)

EnhancedButton.displayName = "EnhancedButton"

export { EnhancedButton, enhancedButtonVariants }

// Usage Examples:
/*
<EnhancedButton variant="primary" size="default">
  Primary Button
</EnhancedButton>

<EnhancedButton variant="secondary" size="lg" leftIcon={<PlusIcon />}>
  Add Item
</EnhancedButton>

<EnhancedButton variant="destructive" loading={isDeleting} loadingText="Deleting...">
  Delete
</EnhancedButton>

<EnhancedButton variant="ghost" size="icon">
  <SettingsIcon />
</EnhancedButton>
*/
