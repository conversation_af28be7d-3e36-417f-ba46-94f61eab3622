'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/lib/contexts/auth-context'
import { getUserTransactions } from '@/lib/supabase/queries'
import { Transaction } from '@/lib/types/database'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Receipt, Plus, ArrowUpRight, ArrowDownRight } from 'lucide-react'
import { TransactionTypeIndicator } from '@/components/ui/status-indicator'

export function RecentTransactions() {
  const { user, profile } = useAuth()
  const [transactions, setTransactions] = useState<Transaction[]>([])
  const [loading, setLoading] = useState(true)

  const currency = profile?.currency_code || 'USD'

  useEffect(() => {
    if (user) {
      loadTransactions()
    }
  }, [user])

  const loadTransactions = async () => {
    if (!user) return

    setLoading(true)
    try {
      const userTransactions = await getUserTransactions(user.id, 5)
      setTransactions(userTransactions)
    } catch (error) {
      console.error('Error loading transactions:', error)
      // Generate sample data for demonstration
      setTransactions(generateSampleTransactions())
    } finally {
      setLoading(false)
    }
  }

  const generateSampleTransactions = (): Transaction[] => {
    const sampleTransactions = [
      {
        id: '1',
        user_id: user?.id || '',
        category_id: '1',
        type: 'expense' as const,
        amount: 45.67,
        currency_code: currency,
        description: 'Grocery shopping',
        merchant_name: 'Whole Foods Market',
        transaction_date: new Date().toISOString().split('T')[0],
        date: new Date().toISOString().split('T')[0], // Add date field that components expect
        receipt_url: null,
        notes: null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        category: {
          id: '1',
          user_id: user?.id || '',
          name: 'Food & Dining',
          color: '#EF4444',
          icon: 'Utensils',
          is_default: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        }
      },
      {
        id: '2',
        user_id: user?.id || '',
        category_id: '2',
        type: 'income' as const,
        amount: 3500.00,
        currency_code: currency,
        description: 'Monthly salary',
        merchant_name: 'Acme Corp',
        transaction_date: new Date(Date.now() - ********).toISOString().split('T')[0],
        date: new Date(Date.now() - ********).toISOString().split('T')[0], // Add date field that components expect
        receipt_url: null,
        notes: null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        category: {
          id: '2',
          user_id: user?.id || '',
          name: 'Salary',
          color: '#10B981',
          icon: 'Banknote',
          is_default: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        }
      },
      {
        id: '3',
        user_id: user?.id || '',
        category_id: '3',
        type: 'expense' as const,
        amount: 89.99,
        currency_code: currency,
        description: 'Monthly internet bill',
        merchant_name: 'Comcast',
        transaction_date: new Date(Date.now() - *********).toISOString().split('T')[0],
        receipt_url: null,
        notes: null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        category: {
          id: '3',
          user_id: user?.id || '',
          name: 'Bills & Utilities',
          color: '#6B7280',
          icon: 'Receipt',
          is_default: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        }
      },
      {
        id: '4',
        user_id: user?.id || '',
        category_id: '4',
        type: 'expense' as const,
        amount: 25.50,
        currency_code: currency,
        description: 'Coffee and pastry',
        merchant_name: 'Starbucks',
        transaction_date: new Date(Date.now() - 259200000).toISOString().split('T')[0],
        receipt_url: null,
        notes: null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        category: {
          id: '4',
          user_id: user?.id || '',
          name: 'Food & Dining',
          color: '#EF4444',
          icon: 'Utensils',
          is_default: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        }
      },
      {
        id: '5',
        user_id: user?.id || '',
        category_id: '5',
        type: 'expense' as const,
        amount: 120.00,
        currency_code: currency,
        description: 'Gas station fill-up',
        merchant_name: 'Shell',
        transaction_date: new Date(Date.now() - 345600000).toISOString().split('T')[0],
        receipt_url: null,
        notes: null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        category: {
          id: '5',
          user_id: user?.id || '',
          name: 'Transportation',
          color: '#F59E0B',
          icon: 'Car',
          is_default: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        }
      },
    ]

    return sampleTransactions
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2,
    }).format(amount)
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    const today = new Date()
    const yesterday = new Date(today)
    yesterday.setDate(yesterday.getDate() - 1)

    if (date.toDateString() === today.toDateString()) {
      return 'Today'
    } else if (date.toDateString() === yesterday.toDateString()) {
      return 'Yesterday'
    } else {
      return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
    }
  }

  if (loading) {
    return (
      <Card className="@container/card">
        <CardHeader>
          <CardTitle>Recent Transactions</CardTitle>
          <CardDescription>Loading your latest transactions...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[1, 2, 3, 4, 5].map((i) => (
              <div key={i} className="flex items-center space-x-4 animate-pulse">
                <div className="w-10 h-10 bg-gray-200 rounded-full"></div>
                <div className="flex-1">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
                <div className="h-4 bg-gray-200 rounded w-16"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  if (transactions.length === 0) {
    return (
      <Card className="@container/card">
        <CardHeader>
          <CardTitle>Recent Transactions</CardTitle>
          <CardDescription>Your latest financial activity</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Receipt className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              No transactions yet
            </h3>
            <p className="text-gray-600 mb-4">
              Start tracking your finances by adding your first transaction
            </p>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add Transaction
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="@container/card">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Recent Transactions</CardTitle>
            <CardDescription>Your latest financial activity</CardDescription>
          </div>
          <Button variant="outline" size="sm">
            View All
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {transactions.map((transaction) => (
            <div key={transaction.id} className="flex items-center space-x-4 p-3 rounded-lg hover:bg-gray-50 transition-colors">
              <TransactionTypeIndicator
                type={transaction.type}
                amount={transaction.amount}
                size="sm"
              />
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-2">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {transaction.description || 'No description'}
                  </p>
                  {transaction.category && (
                    <Badge variant="secondary" className="text-xs">
                      {transaction.category.name}
                    </Badge>
                  )}
                </div>
                <div className="flex items-center space-x-2 text-xs text-gray-500">
                  {transaction.merchant_name && (
                    <span>{transaction.merchant_name}</span>
                  )}
                  <span>•</span>
                  <span>{formatDate(transaction.transaction_date)}</span>
                </div>
              </div>
              
              <div className="text-right">
                <p className={`text-sm font-semibold ${
                  transaction.type === 'income' ? 'text-green-600' : 'text-red-600'
                }`}>
                  {transaction.type === 'income' ? '+' : '-'}{formatCurrency(transaction.amount)}
                </p>
              </div>
            </div>
          ))}

          <div className="pt-4 border-t">
            <Button variant="ghost" className="w-full">
              View All Transactions
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
