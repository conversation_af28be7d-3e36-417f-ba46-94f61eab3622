export type TransactionType = 'income' | 'expense';
export type RecurringFrequency = 'daily' | 'weekly' | 'monthly' | 'yearly';
export type GoalStatus = 'active' | 'completed' | 'paused';
export type LoanType = 'bank_loan' | 'personal_debt';
export type Category = 'food' | 'transport' | 'shopping' | 'utilities' | 'entertainment' | 'health' | 'other';

export interface UserProfile {
  id: string;
  full_name: string | null;
  avatar_url: string | null;
  currency_code: string;
  monthly_income: number | null;
  salary_payment_date: number | null;
  created_at: string;
  updated_at: string;
}

export interface CategoryEntity {
  id: string;
  user_id: string;
  name: string;
  color: string | null;
  icon: string | null;
  is_default: boolean;
  created_at: string;
  updated_at: string;
}

export interface Transaction {
  id: string;
  user_id: string;
  category_id: string | null;
  type: TransactionType;
  amount: number;
  currency_code: string;
  description: string | null;
  merchant_name: string | null;
  receipt_url: string | null;
  notes: string | null;
  date: string; // Add date field that components expect
  transaction_date: string; // Add transaction_date field that components expect
  created_at: string;
  updated_at: string;
  category?: Category;
  categoryEntity?: CategoryEntity; // Renamed to avoid conflicts
}

export interface RecurringPayment {
  id: string;
  user_id: string;
  category_id: string | null;
  name: string;
  amount: number;
  currency_code: string;
  frequency: RecurringFrequency;
  start_date: string;
  end_date: string | null;
  next_payment_date: string; // Add missing field that components expect
  is_active: boolean;
  description: string | null;
  created_at: string;
  updated_at: string;
  category?: Category;
  categoryEntity?: CategoryEntity;
}

export interface Goal {
  id: string;
  user_id: string;
  name: string;
  description: string | null;
  target_amount: number;
  current_amount: number;
  currency_code: string;
  target_date: string | null;
  status: GoalStatus;
  category: string; // Add missing category field that components expect
  created_at: string;
  updated_at: string;
}

export interface Loan {
  id: string;
  user_id: string;
  type: LoanType;
  loan_type: LoanType; // Add alias for components that expect this field name
  name: string;
  principal_amount: number;
  current_balance: number;
  interest_rate: number | null;
  currency_code: string;
  start_date: string;
  end_date: string | null;
  monthly_payment: number | null;
  term_months: number | null; // Add missing field that components expect
  lender_name: string | null;
  lender: string | null; // Add alias for components that expect this field name
  category: string; // Add missing category field that components expect
  description: string | null;
  created_at: string;
  updated_at: string;
}

export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp?: string;
  metadata?: {
    confidence?: number;
    tools_used?: string[];
    error?: string;
  };
}

export interface AIConversation {
  id: string;
  user_id: string;
  title: string | null;
  messages: ChatMessage[];
  created_at: string;
  updated_at: string;
}

export interface User {
  id: string;
  email: string;
  created_at: string;
  updated_at: string;
}


export interface UserSettings {
  id: string;
  user_id: string;
  dashboard_widgets: Record<string, any>;
  ai_preferences: Record<string, any>;
  notification_settings: Record<string, any>;
  theme_preferences: Record<string, any>;
  created_at: string;
  updated_at: string;
}

// Database schema type for Supabase
export interface Database {
  public: {
    Tables: {
      user_profiles: {
        Row: UserProfile;
        Insert: Omit<UserProfile, 'created_at' | 'updated_at'>;
        Update: Partial<Omit<UserProfile, 'id' | 'created_at' | 'updated_at'>>;
      };
      categories: {
        Row: CategoryEntity;
        Insert: Omit<CategoryEntity, 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Omit<CategoryEntity, 'id' | 'user_id' | 'created_at' | 'updated_at'>>;
      };
      transactions: {
        Row: Transaction;
        Insert: Omit<Transaction, 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Omit<Transaction, 'id' | 'user_id' | 'created_at' | 'updated_at'>>;
      };
      recurring_payments: {
        Row: RecurringPayment;
        Insert: Omit<RecurringPayment, 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Omit<RecurringPayment, 'id' | 'user_id' | 'created_at' | 'updated_at'>>;
      };
      goals: {
        Row: Goal;
        Insert: Omit<Goal, 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Omit<Goal, 'id' | 'user_id' | 'created_at' | 'updated_at'>>;
      };
      loans: {
        Row: Loan;
        Insert: Omit<Loan, 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Omit<Loan, 'id' | 'user_id' | 'created_at' | 'updated_at'>>;
      };
      ai_conversations: {
        Row: AIConversation;
        Insert: Omit<AIConversation, 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Omit<AIConversation, 'id' | 'user_id' | 'created_at' | 'updated_at'>>;
      };
      user_settings: {
        Row: UserSettings;
        Insert: Omit<UserSettings, 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Omit<UserSettings, 'id' | 'user_id' | 'created_at' | 'updated_at'>>;
      };
    };
  };
}
