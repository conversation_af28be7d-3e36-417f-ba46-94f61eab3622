'use client'

import { useState, useRef, useEffect } from 'react'
import { useChat } from '@ai-sdk/react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  Bot,
  User,
  Send,
  Loader2,
  DollarSign,
  Target,
  TrendingUp,
  PiggyBank,
  CreditCard,
  Lightbulb
} from 'lucide-react'
import ReactMarkdown from 'react-markdown'
import { User as UserType, UserProfile } from '@/lib/types/database'

interface FinancialChatInterfaceProps {
  user: UserType | null
  profile: UserProfile | null
}

const suggestedQuestions = [
  {
    icon: DollarSign,
    text: "How can I improve my budget?",
    category: "Budgeting"
  },
  {
    icon: Target,
    text: "Help me set realistic financial goals",
    category: "Goals"
  },
  {
    icon: TrendingUp,
    text: "Analyze my spending patterns",
    category: "Analysis"
  },
  {
    icon: PiggyBank,
    text: "What's the best way to save money?",
    category: "Savings"
  },
  {
    icon: CreditCard,
    text: "How should I manage my debt?",
    category: "Debt"
  },
  {
    icon: Lightbulb,
    text: "Give me personalized financial tips",
    category: "Tips"
  }
]

export function FinancialChatInterface({ user, profile }: FinancialChatInterfaceProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const [showSuggestions, setShowSuggestions] = useState(true)

  // Create financial context from user data
  const financialContext = `
User Profile:
- Name: ${profile?.full_name || 'User'}
- Currency: ${profile?.currency_code || 'USD'}
- Monthly Income: ${profile?.monthly_income ? `${profile.currency_code || 'USD'} ${profile.monthly_income}` : 'Not specified'}
- Salary Payment Date: ${profile?.salary_payment_date ? `${profile.salary_payment_date} of each month` : 'Not specified'}

You are a personal financial advisor AI assistant. Provide helpful, personalized financial advice based on the user's profile and questions. Be encouraging, practical, and specific in your recommendations.
  `.trim()

  const { messages, input, handleInputChange, handleSubmit, isLoading, error } = useChat({
    api: '/api/financial-chat',
    body: {
      financialContext,
      userId: user?.id
    },
    initialMessages: [
      {
        id: 'welcome',
        role: 'assistant',
        content: `Hello ${profile?.full_name || 'there'}! 👋 I'm your personal financial advisor AI. I'm here to help you with budgeting, saving, investing, and achieving your financial goals. 

What would you like to discuss today? You can ask me anything about:
• Creating and managing budgets
• Setting and tracking financial goals  
• Analyzing spending patterns
• Debt management strategies
• Saving and investment tips
• Personalized financial advice

Feel free to ask me anything or choose from the suggested questions below!`
      }
    ]
  })

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages])

  const handleSuggestedQuestion = (question: string) => {
    handleInputChange({ target: { value: question } } as any)
    setShowSuggestions(false)
  }

  const handleFormSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (!input.trim() || isLoading) return
    setShowSuggestions(false)
    handleSubmit(e)
  }

  return (
    <Card className="h-[700px] flex flex-col">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Bot className="w-5 h-5 text-primary" />
          Financial Advisor Chat
        </CardTitle>
        <CardDescription>
          Get personalized financial advice and guidance from your AI assistant
        </CardDescription>
      </CardHeader>

      <CardContent className="flex-1 flex flex-col p-0">
        {/* Messages Area */}
        <ScrollArea className="flex-1 p-4">
          <div className="space-y-4">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`flex items-start gap-3 max-w-[80%] ${
                    message.role === 'user' ? 'flex-row-reverse' : 'flex-row'
                  }`}
                >
                  <div
                    className={`p-2 rounded-full ${
                      message.role === 'user' 
                        ? 'bg-primary text-primary-foreground' 
                        : 'bg-muted'
                    }`}
                  >
                    {message.role === 'user' ? (
                      <User className="w-4 h-4" />
                    ) : (
                      <Bot className="w-4 h-4" />
                    )}
                  </div>
                  <div
                    className={`p-4 rounded-lg ${
                      message.role === 'user'
                        ? 'bg-primary text-primary-foreground'
                        : 'bg-muted'
                    }`}
                  >
                    <div className="prose prose-sm max-w-none dark:prose-invert">
                      <ReactMarkdown>{message.content}</ReactMarkdown>
                    </div>
                    <div className="text-xs opacity-70 mt-2">
                      {new Date().toLocaleTimeString()}
                    </div>
                  </div>
                </div>
              </div>
            ))}

            {isLoading && (
              <div className="flex justify-start">
                <div className="flex items-start gap-3 max-w-[80%]">
                  <div className="p-2 rounded-full bg-muted">
                    <Bot className="w-4 h-4" />
                  </div>
                  <div className="p-4 rounded-lg bg-muted">
                    <div className="flex items-center gap-2">
                      <Loader2 className="w-4 h-4 animate-spin" />
                      <span className="text-sm">Thinking...</span>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {error && (
              <Alert variant="destructive">
                <AlertDescription>
                  Sorry, I encountered an error. Please try again.
                </AlertDescription>
              </Alert>
            )}

            <div ref={messagesEndRef} />
          </div>
        </ScrollArea>

        {/* Suggested Questions */}
        {showSuggestions && messages.length <= 1 && (
          <div className="p-4 border-t bg-muted/30">
            <h4 className="text-sm font-medium mb-3">Suggested Questions:</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
              {suggestedQuestions.map((suggestion, index) => (
                <Button
                  key={index}
                  variant="outline"
                  size="sm"
                  className="justify-start h-auto p-3 text-left"
                  onClick={() => handleSuggestedQuestion(suggestion.text)}
                >
                  <suggestion.icon className="w-4 h-4 mr-2 flex-shrink-0" />
                  <div>
                    <div className="text-sm">{suggestion.text}</div>
                    <Badge variant="secondary" className="text-xs mt-1">
                      {suggestion.category}
                    </Badge>
                  </div>
                </Button>
              ))}
            </div>
          </div>
        )}

        {/* Input Area */}
        <form onSubmit={handleFormSubmit} className="p-4 border-t">
          <div className="flex gap-2">
            <Input
              value={input}
              onChange={handleInputChange}
              placeholder="Ask me anything about your finances..."
              disabled={isLoading}
              className="flex-1"
            />
            <Button type="submit" disabled={isLoading || !input.trim()}>
              {isLoading ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Send className="w-4 h-4" />
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
