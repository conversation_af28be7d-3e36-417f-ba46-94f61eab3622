'use client'

import { useState } from 'react'
import { AppLayout } from '@/components/layouts/app-layout'
import { Loan } from '@/lib/types/database'
import { LoanForm } from '@/components/loans/loan-form'
import { LoansTable } from '@/components/loans/loans-table'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { CreditCard, Plus, Building, User, Calendar } from 'lucide-react'

export default function LoansDebtsPage() {
  const [showForm, setShowForm] = useState(false)
  const [editingLoan, setEditingLoan] = useState<Loan | undefined>()
  const [refreshTrigger, setRefreshTrigger] = useState(0)
  const [currentLoanType, setCurrentLoanType] = useState<'bank_loan' | 'personal_debt'>('bank_loan')

  const handleAddLoan = (loanType: 'bank_loan' | 'personal_debt') => {
    setCurrentLoanType(loanType)
    setEditingLoan(undefined)
    setShowForm(true)
  }

  const handleEditLoan = (loan: Loan) => {
    setCurrentLoanType(loan.loan_type as 'bank_loan' | 'personal_debt')
    setEditingLoan(loan)
    setShowForm(true)
  }

  const handleFormSuccess = () => {
    setShowForm(false)
    setEditingLoan(undefined)
    setRefreshTrigger(prev => prev + 1)
  }

  const handleFormCancel = () => {
    setShowForm(false)
    setEditingLoan(undefined)
  }

  return (
    <AppLayout>
      <div className="max-w-6xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Loans & Debts</h1>
            <p className="text-muted-foreground">
              Track and manage your loans, debts, and payment schedules
            </p>
          </div>
        </div>

        <Tabs defaultValue="bank-loans" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="bank-loans">Bank Loans</TabsTrigger>
            <TabsTrigger value="personal-debts">Personal Debts</TabsTrigger>
            <TabsTrigger value="payment-calendar">Payment Calendar</TabsTrigger>
          </TabsList>

          <TabsContent value="bank-loans">
            <LoansTable
              onEditLoan={handleEditLoan}
              onAddLoan={() => handleAddLoan('bank_loan')}
              refreshTrigger={refreshTrigger}
              loanType="bank_loan"
            />
          </TabsContent>

          <TabsContent value="personal-debts">
            <LoansTable
              onEditLoan={handleEditLoan}
              onAddLoan={() => handleAddLoan('personal_debt')}
              refreshTrigger={refreshTrigger}
              loanType="personal_debt"
            />
          </TabsContent>

          <TabsContent value="payment-calendar">
            <Card>
              <CardHeader>
                <div className="flex items-center space-x-2">
                  <Calendar className="h-5 w-5" />
                  <CardTitle>Payment Calendar</CardTitle>
                </div>
                <CardDescription>
                  View all upcoming loan and debt payments
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-12">
                  <Calendar className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-xl font-semibold mb-2">
                    Payment Calendar Coming Soon
                  </h3>
                  <p className="text-muted-foreground">
                    This feature will show all your upcoming loan and debt payments in a calendar view
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Loan Form Dialog */}
        <Dialog open={showForm} onOpenChange={setShowForm}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>
                {editingLoan ? 'Edit' : 'Add New'} {currentLoanType === 'bank_loan' ? 'Loan' : 'Debt'}
              </DialogTitle>
            </DialogHeader>
            <LoanForm
              loan={editingLoan}
              onSuccess={handleFormSuccess}
              onCancel={handleFormCancel}
              loanType={currentLoanType}
            />
          </DialogContent>
        </Dialog>
      </div>
    </AppLayout>
  )
}
