'use client'

import React, { useState, useMemo } from 'react'
import { cn } from '@/lib/utils'
import { TrendingUp, TrendingDown, Minus, Info, Download, Filter } from 'lucide-react'

// Enhanced Data Table with Professional Styling
interface DataTableColumn {
  key: string
  label: string
  type?: 'text' | 'number' | 'currency' | 'date' | 'status' | 'trend'
  sortable?: boolean
  width?: string
  align?: 'left' | 'center' | 'right'
}

interface DataTableProps {
  columns: DataTableColumn[]
  data: Record<string, any>[]
  className?: string
  showExport?: boolean
  showFilter?: boolean
  onRowClick?: (row: Record<string, any>) => void
}

export function AdvancedDataTable({ 
  columns, 
  data, 
  className,
  showExport = true,
  showFilter = true,
  onRowClick 
}: DataTableProps) {
  const [sortColumn, setSortColumn] = useState<string | null>(null)
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc')
  const [filterText, setFilterText] = useState('')
  
  const filteredData = useMemo(() => {
    if (!filterText) return data
    
    return data.filter(row =>
      Object.values(row).some(value =>
        String(value).toLowerCase().includes(filterText.toLowerCase())
      )
    )
  }, [data, filterText])
  
  const sortedData = useMemo(() => {
    if (!sortColumn) return filteredData
    
    return [...filteredData].sort((a, b) => {
      const aVal = a[sortColumn]
      const bVal = b[sortColumn]
      
      if (typeof aVal === 'number' && typeof bVal === 'number') {
        return sortDirection === 'asc' ? aVal - bVal : bVal - aVal
      }
      
      const aStr = String(aVal).toLowerCase()
      const bStr = String(bVal).toLowerCase()
      
      if (sortDirection === 'asc') {
        return aStr.localeCompare(bStr)
      } else {
        return bStr.localeCompare(aStr)
      }
    })
  }, [filteredData, sortColumn, sortDirection])
  
  const handleSort = (columnKey: string) => {
    if (sortColumn === columnKey) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortColumn(columnKey)
      setSortDirection('asc')
    }
  }
  
  const formatCellValue = (value: any, type: string = 'text') => {
    switch (type) {
      case 'currency':
        return new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: 'USD'
        }).format(value)
      
      case 'number':
        return new Intl.NumberFormat('en-US').format(value)
      
      case 'date':
        return new Date(value).toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short',
          day: 'numeric'
        })
      
      case 'status':
        return (
          <span className={cn(
            "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",
            value === 'active' && "bg-success/10 text-success",
            value === 'pending' && "bg-warning/10 text-warning",
            value === 'inactive' && "bg-neutral-100 text-neutral-600",
            value === 'completed' && "bg-primary/10 text-primary"
          )}>
            {value}
          </span>
        )
      
      case 'trend':
        const isPositive = value > 0
        const isNegative = value < 0
        return (
          <div className="flex items-center gap-1">
            {isPositive && <TrendingUp className="w-4 h-4 text-success" />}
            {isNegative && <TrendingDown className="w-4 h-4 text-error" />}
            {!isPositive && !isNegative && <Minus className="w-4 h-4 text-neutral-400" />}
            <span className={cn(
              "text-sm font-medium",
              isPositive && "text-success",
              isNegative && "text-error",
              !isPositive && !isNegative && "text-neutral-500"
            )}>
              {Math.abs(value)}%
            </span>
          </div>
        )
      
      default:
        return value
    }
  }
  
  const exportData = () => {
    const csv = [
      columns.map(col => col.label).join(','),
      ...sortedData.map(row =>
        columns.map(col => {
          const value = row[col.key]
          return typeof value === 'string' && value.includes(',') 
            ? `"${value}"` 
            : value
        }).join(',')
      )
    ].join('\n')
    
    const blob = new Blob([csv], { type: 'text/csv' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'data-export.csv'
    a.click()
    URL.revokeObjectURL(url)
  }
  
  return (
    <div className={cn("space-y-4", className)}>
      {/* Table Controls */}
      {(showFilter || showExport) && (
        <div className="flex items-center justify-between gap-4">
          {showFilter && (
            <div className="relative flex-1 max-w-sm">
              <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-text-tertiary" />
              <input
                type="text"
                placeholder="Filter data..."
                value={filterText}
                onChange={(e) => setFilterText(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-border-default rounded-lg bg-surface text-text-primary placeholder:text-text-tertiary focus:outline-none focus:border-primary focus:ring-2 focus:ring-primary/20"
              />
            </div>
          )}
          
          {showExport && (
            <button
              onClick={exportData}
              className="inline-flex items-center gap-2 px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-600 transition-colors text-sm font-medium"
            >
              <Download className="w-4 h-4" />
              Export CSV
            </button>
          )}
        </div>
      )}
      
      {/* Table */}
      <div className="overflow-hidden border border-border-default rounded-lg bg-surface elevation-2">
        <div className="overflow-x-auto">
          <table className="w-full">
            {/* Header */}
            <thead className="bg-neutral-50 dark:bg-neutral-800">
              <tr>
                {columns.map((column) => (
                  <th
                    key={column.key}
                    className={cn(
                      "px-6 py-4 text-left text-xs font-semibold text-text-secondary uppercase tracking-wider",
                      column.sortable && "cursor-pointer hover:bg-neutral-100 dark:hover:bg-neutral-700 transition-colors",
                      column.align === 'center' && "text-center",
                      column.align === 'right' && "text-right"
                    )}
                    style={{ width: column.width }}
                    onClick={() => column.sortable && handleSort(column.key)}
                  >
                    <div className="flex items-center gap-2">
                      {column.label}
                      {column.sortable && sortColumn === column.key && (
                        <span className="text-primary">
                          {sortDirection === 'asc' ? '↑' : '↓'}
                        </span>
                      )}
                    </div>
                  </th>
                ))}
              </tr>
            </thead>
            
            {/* Body */}
            <tbody className="divide-y divide-border-subtle">
              {sortedData.map((row, index) => (
                <tr
                  key={index}
                  className={cn(
                    "hover:bg-neutral-50 dark:hover:bg-neutral-800 transition-colors",
                    onRowClick && "cursor-pointer"
                  )}
                  onClick={() => onRowClick?.(row)}
                >
                  {columns.map((column) => (
                    <td
                      key={column.key}
                      className={cn(
                        "px-6 py-4 whitespace-nowrap text-sm text-text-primary",
                        column.align === 'center' && "text-center",
                        column.align === 'right' && "text-right"
                      )}
                    >
                      {formatCellValue(row[column.key], column.type)}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        
        {/* Empty State */}
        {sortedData.length === 0 && (
          <div className="text-center py-12">
            <div className="text-text-tertiary mb-2">
              <Info className="w-8 h-8 mx-auto mb-2" />
              No data found
            </div>
            <p className="text-sm text-text-secondary">
              {filterText ? 'Try adjusting your filter criteria' : 'No data available to display'}
            </p>
          </div>
        )}
      </div>
      
      {/* Table Footer */}
      {sortedData.length > 0 && (
        <div className="flex items-center justify-between text-sm text-text-secondary">
          <div>
            Showing {sortedData.length} of {data.length} entries
            {filterText && ` (filtered from ${data.length} total entries)`}
          </div>
        </div>
      )}
    </div>
  )
}

// Enhanced Progress Ring with Animations
interface ProgressRingProps {
  value: number
  max?: number
  size?: number
  strokeWidth?: number
  className?: string
  label?: string
  showValue?: boolean
  color?: 'primary' | 'success' | 'warning' | 'error'
  animated?: boolean
}

export function AdvancedProgressRing({
  value,
  max = 100,
  size = 120,
  strokeWidth = 8,
  className,
  label,
  showValue = true,
  color = 'primary',
  animated = true
}: ProgressRingProps) {
  const percentage = Math.min((value / max) * 100, 100)
  const radius = (size - strokeWidth) / 2
  const circumference = radius * 2 * Math.PI
  const strokeDasharray = circumference
  const strokeDashoffset = circumference - (percentage / 100) * circumference
  
  const colorClasses = {
    primary: 'stroke-primary',
    success: 'stroke-success',
    warning: 'stroke-warning',
    error: 'stroke-error'
  }
  
  return (
    <div className={cn("relative inline-flex items-center justify-center", className)}>
      <svg
        width={size}
        height={size}
        className="transform -rotate-90"
      >
        {/* Background circle */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke="currentColor"
          strokeWidth={strokeWidth}
          fill="none"
          className="text-neutral-200 dark:text-neutral-700"
        />
        
        {/* Progress circle */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke="currentColor"
          strokeWidth={strokeWidth}
          fill="none"
          strokeDasharray={strokeDasharray}
          strokeDashoffset={strokeDashoffset}
          strokeLinecap="round"
          className={cn(
            colorClasses[color],
            animated && "transition-all duration-1000 ease-out"
          )}
        />
        
        {/* Glow effect */}
        {animated && (
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke="currentColor"
            strokeWidth={strokeWidth / 2}
            fill="none"
            strokeDasharray={strokeDasharray}
            strokeDashoffset={strokeDashoffset}
            strokeLinecap="round"
            className={cn(colorClasses[color], "opacity-30 animate-pulse")}
          />
        )}
      </svg>
      
      {/* Center content */}
      <div className="absolute inset-0 flex flex-col items-center justify-center text-center">
        {showValue && (
          <span className="text-2xl font-bold text-text-primary">
            {Math.round(percentage)}%
          </span>
        )}
        {label && (
          <span className="text-sm text-text-secondary mt-1 max-w-20 leading-tight">
            {label}
          </span>
        )}
      </div>
    </div>
  )
}

// Interactive Metric Card
interface MetricCardProps {
  title: string
  value: string | number
  change?: number
  changeLabel?: string
  icon?: React.ReactNode
  trend?: 'up' | 'down' | 'neutral'
  className?: string
  onClick?: () => void
}

export function InteractiveMetricCard({
  title,
  value,
  change,
  changeLabel,
  icon,
  trend,
  className,
  onClick
}: MetricCardProps) {
  const isClickable = !!onClick
  
  return (
    <div
      className={cn(
        "card-premium-elevated p-6 space-y-4 transition-all duration-300",
        isClickable && "cursor-pointer hover:elevation-4 hover:scale-105",
        className
      )}
      onClick={onClick}
    >
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-sm font-medium text-text-secondary">{title}</h3>
        {icon && (
          <div className="text-text-tertiary">
            {icon}
          </div>
        )}
      </div>
      
      {/* Value */}
      <div className="space-y-2">
        <div className="text-3xl font-bold text-text-primary">
          {typeof value === 'number' 
            ? new Intl.NumberFormat('en-US').format(value)
            : value
          }
        </div>
        
        {/* Change Indicator */}
        {change !== undefined && (
          <div className="flex items-center gap-2">
            <div className={cn(
              "flex items-center gap-1 text-sm font-medium",
              trend === 'up' && "text-success",
              trend === 'down' && "text-error",
              trend === 'neutral' && "text-text-secondary"
            )}>
              {trend === 'up' && <TrendingUp className="w-4 h-4" />}
              {trend === 'down' && <TrendingDown className="w-4 h-4" />}
              {trend === 'neutral' && <Minus className="w-4 h-4" />}
              
              <span>
                {change > 0 ? '+' : ''}{change}%
              </span>
            </div>
            
            {changeLabel && (
              <span className="text-sm text-text-tertiary">
                {changeLabel}
              </span>
            )}
          </div>
        )}
      </div>
      
      {/* Hover Effect Indicator */}
      {isClickable && (
        <div className="opacity-0 group-hover:opacity-100 transition-opacity">
          <div className="w-full h-1 bg-gradient-to-r from-primary to-primary-light rounded-full" />
        </div>
      )}
    </div>
  )
}

// Smart Data Insights Component
interface DataInsightProps {
  title: string
  insight: string
  confidence: number
  trend: 'positive' | 'negative' | 'neutral'
  actionable?: boolean
  onAction?: () => void
  className?: string
}

export function SmartDataInsight({
  title,
  insight,
  confidence,
  trend,
  actionable = false,
  onAction,
  className
}: DataInsightProps) {
  const trendColors = {
    positive: 'text-success',
    negative: 'text-error',
    neutral: 'text-text-secondary'
  }
  
  const trendIcons = {
    positive: <TrendingUp className="w-4 h-4" />,
    negative: <TrendingDown className="w-4 h-4" />,
    neutral: <Minus className="w-4 h-4" />
  }
  
  return (
    <div className={cn(
      "glass-morphism p-4 rounded-lg border border-border-subtle space-y-3",
      className
    )}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <div className={trendColors[trend]}>
            {trendIcons[trend]}
          </div>
          <h4 className="font-medium text-text-primary">{title}</h4>
        </div>
        
        {/* Confidence Indicator */}
        <div className="flex items-center gap-2">
          <div className="text-xs text-text-tertiary">
            {confidence}% confidence
          </div>
          <div className="w-12 h-2 bg-neutral-200 rounded-full overflow-hidden">
            <div 
              className="h-full bg-primary transition-all duration-500"
              style={{ width: `${confidence}%` }}
            />
          </div>
        </div>
      </div>
      
      {/* Insight Text */}
      <p className="text-sm text-text-secondary leading-relaxed">
        {insight}
      </p>
      
      {/* Action Button */}
      {actionable && onAction && (
        <button
          onClick={onAction}
          className="text-sm text-primary hover:text-primary-600 font-medium transition-colors"
        >
          Take Action →
        </button>
      )}
    </div>
  )
}
