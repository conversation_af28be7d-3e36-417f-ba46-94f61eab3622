'use client'

import React from 'react'
import { 
  AlertTriangle, 
  RefreshCw, 
  Wifi, 
  WifiOff, 
  FileX, 
  Plus,
  Search,
  TrendingUp,
  Target,
  CreditCard,
  CheckCircle,
  XCircle,
  Clock
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { EnhancedButton } from '@/components/ui/enhanced-button'

// Error State Component
interface ErrorStateProps {
  title?: string
  description?: string
  error?: Error | string
  onRetry?: () => void
  onGoBack?: () => void
  className?: string
  variant?: 'network' | 'server' | 'permission' | 'generic'
}

export function ErrorState({ 
  title,
  description,
  error,
  onRetry,
  onGoBack,
  className,
  variant = 'generic'
}: ErrorStateProps) {
  const getErrorContent = () => {
    switch (variant) {
      case 'network':
        return {
          icon: <WifiOff className="w-16 h-16 text-error" />,
          title: title || 'Connection Problem',
          description: description || 'Please check your internet connection and try again.'
        }
      case 'server':
        return {
          icon: <AlertTriangle className="w-16 h-16 text-error" />,
          title: title || 'Server Error',
          description: description || 'Something went wrong on our end. Please try again in a moment.'
        }
      case 'permission':
        return {
          icon: <XCircle className="w-16 h-16 text-error" />,
          title: title || 'Access Denied',
          description: description || 'You don\'t have permission to access this resource.'
        }
      default:
        return {
          icon: <AlertTriangle className="w-16 h-16 text-error" />,
          title: title || 'Something went wrong',
          description: description || 'An unexpected error occurred. Please try again.'
        }
    }
  }
  
  const content = getErrorContent()
  
  return (
    <div className={cn("flex flex-col items-center justify-center p-8 text-center", className)}>
      <div className="mb-6 animate-pulse">
        {content.icon}
      </div>
      
      <h3 className="text-heading-medium text-text-primary mb-2">
        {content.title}
      </h3>
      
      <p className="text-body-medium text-text-secondary mb-6 max-w-md">
        {content.description}
      </p>
      
      {/* Error Details (Development) */}
      {process.env.NODE_ENV === 'development' && error && (
        <details className="mb-6 text-left w-full max-w-md">
          <summary className="text-sm text-text-tertiary cursor-pointer hover:text-text-secondary">
            Error Details
          </summary>
          <pre className="mt-2 p-3 bg-neutral-100 rounded text-xs text-text-secondary overflow-auto">
            {typeof error === 'string' ? error : error.message}
          </pre>
        </details>
      )}
      
      <div className="flex gap-3">
        {onGoBack && (
          <EnhancedButton variant="secondary" onClick={onGoBack}>
            Go Back
          </EnhancedButton>
        )}
        {onRetry && (
          <EnhancedButton 
            variant="primary" 
            onClick={onRetry}
            leftIcon={<RefreshCw className="w-4 h-4" />}
          >
            Try Again
          </EnhancedButton>
        )}
      </div>
    </div>
  )
}

// Empty State Component
interface EmptyStateProps {
  title: string
  description: string
  icon?: React.ReactNode
  action?: {
    label: string
    onClick: () => void
    variant?: 'primary' | 'secondary'
  }
  secondaryAction?: {
    label: string
    onClick: () => void
  }
  className?: string
  variant?: 'transactions' | 'goals' | 'search' | 'generic'
}

export function EmptyState({ 
  title,
  description,
  icon,
  action,
  secondaryAction,
  className,
  variant = 'generic'
}: EmptyStateProps) {
  const getDefaultIcon = () => {
    switch (variant) {
      case 'transactions':
        return <CreditCard className="w-16 h-16 text-text-tertiary" />
      case 'goals':
        return <Target className="w-16 h-16 text-text-tertiary" />
      case 'search':
        return <Search className="w-16 h-16 text-text-tertiary" />
      default:
        return <FileX className="w-16 h-16 text-text-tertiary" />
    }
  }
  
  return (
    <div className={cn("flex flex-col items-center justify-center p-8 text-center", className)}>
      <div className="mb-6 opacity-60">
        {icon || getDefaultIcon()}
      </div>
      
      <h3 className="text-heading-medium text-text-primary mb-2">
        {title}
      </h3>
      
      <p className="text-body-medium text-text-secondary mb-6 max-w-md">
        {description}
      </p>
      
      <div className="flex flex-col sm:flex-row gap-3">
        {action && (
          <EnhancedButton 
            variant={action.variant || 'primary'}
            onClick={action.onClick}
            leftIcon={<Plus className="w-4 h-4" />}
          >
            {action.label}
          </EnhancedButton>
        )}
        {secondaryAction && (
          <EnhancedButton 
            variant="secondary"
            onClick={secondaryAction.onClick}
          >
            {secondaryAction.label}
          </EnhancedButton>
        )}
      </div>
    </div>
  )
}

// Loading State Component
interface LoadingStateProps {
  title?: string
  description?: string
  className?: string
  variant?: 'spinner' | 'skeleton' | 'pulse'
}

export function LoadingState({ 
  title = 'Loading...',
  description,
  className,
  variant = 'spinner'
}: LoadingStateProps) {
  const renderLoader = () => {
    switch (variant) {
      case 'skeleton':
        return (
          <div className="space-y-4 w-full max-w-md">
            <div className="skeleton h-4 w-3/4 mx-auto" />
            <div className="skeleton h-4 w-1/2 mx-auto" />
            <div className="skeleton h-32 w-full" />
          </div>
        )
      case 'pulse':
        return (
          <div className="w-16 h-16 bg-primary/20 rounded-full pulse-loading" />
        )
      default:
        return (
          <div className="spinner-premium spinner-lg" />
        )
    }
  }
  
  return (
    <div className={cn("flex flex-col items-center justify-center p-8 text-center", className)}>
      <div className="mb-6">
        {renderLoader()}
      </div>
      
      <h3 className="text-heading-medium text-text-primary mb-2">
        {title}
      </h3>
      
      {description && (
        <p className="text-body-medium text-text-secondary max-w-md">
          {description}
        </p>
      )}
    </div>
  )
}

// Offline State Component
interface OfflineStateProps {
  onRetry?: () => void
  className?: string
}

export function OfflineState({ onRetry, className }: OfflineStateProps) {
  return (
    <div className={cn("flex flex-col items-center justify-center p-8 text-center", className)}>
      <div className="mb-6">
        <WifiOff className="w-16 h-16 text-warning" />
      </div>
      
      <h3 className="text-heading-medium text-text-primary mb-2">
        You're offline
      </h3>
      
      <p className="text-body-medium text-text-secondary mb-6 max-w-md">
        Some features may not be available. We'll sync your data when you're back online.
      </p>
      
      <div className="flex items-center gap-2 text-sm text-text-tertiary">
        <div className="w-2 h-2 bg-warning rounded-full animate-pulse" />
        Waiting for connection...
      </div>
      
      {onRetry && (
        <EnhancedButton 
          variant="secondary" 
          onClick={onRetry}
          className="mt-4"
          leftIcon={<RefreshCw className="w-4 h-4" />}
        >
          Check Connection
        </EnhancedButton>
      )}
    </div>
  )
}

// Success State Component
interface SuccessStateProps {
  title: string
  description?: string
  onContinue?: () => void
  onClose?: () => void
  className?: string
  showConfetti?: boolean
}

export function SuccessState({ 
  title,
  description,
  onContinue,
  onClose,
  className,
  showConfetti = false
}: SuccessStateProps) {
  return (
    <div className={cn("flex flex-col items-center justify-center p-8 text-center", className)}>
      {showConfetti && (
        <div className="confetti">
          {Array.from({ length: 50 }).map((_, i) => (
            <div
              key={i}
              className="confetti-piece"
              style={{
                left: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 3}s`,
                backgroundColor: ['#10B981', '#3B82F6', '#F59E0B', '#EF4444'][Math.floor(Math.random() * 4)]
              }}
            />
          ))}
        </div>
      )}
      
      <div className="mb-6">
        <div className="success-checkmark" />
      </div>
      
      <h3 className="text-heading-medium text-text-primary mb-2">
        {title}
      </h3>
      
      {description && (
        <p className="text-body-medium text-text-secondary mb-6 max-w-md">
          {description}
        </p>
      )}
      
      <div className="flex gap-3">
        {onClose && (
          <EnhancedButton variant="secondary" onClick={onClose}>
            Close
          </EnhancedButton>
        )}
        {onContinue && (
          <EnhancedButton variant="primary" onClick={onContinue}>
            Continue
          </EnhancedButton>
        )}
      </div>
    </div>
  )
}

// Timeout State Component
interface TimeoutStateProps {
  onRetry?: () => void
  onCancel?: () => void
  className?: string
}

export function TimeoutState({ onRetry, onCancel, className }: TimeoutStateProps) {
  return (
    <div className={cn("flex flex-col items-center justify-center p-8 text-center", className)}>
      <div className="mb-6">
        <Clock className="w-16 h-16 text-warning" />
      </div>
      
      <h3 className="text-heading-medium text-text-primary mb-2">
        Request Timeout
      </h3>
      
      <p className="text-body-medium text-text-secondary mb-6 max-w-md">
        The request is taking longer than expected. This might be due to a slow connection.
      </p>
      
      <div className="flex gap-3">
        {onCancel && (
          <EnhancedButton variant="secondary" onClick={onCancel}>
            Cancel
          </EnhancedButton>
        )}
        {onRetry && (
          <EnhancedButton 
            variant="primary" 
            onClick={onRetry}
            leftIcon={<RefreshCw className="w-4 h-4" />}
          >
            Try Again
          </EnhancedButton>
        )}
      </div>
    </div>
  )
}
