'use client'

import { useState } from 'react'
import { AppLayout } from '@/components/layouts/app-layout'
import { RecurringPayment } from '@/lib/types/database'
import { RecurringPaymentForm } from '@/components/recurring-payments/recurring-payment-form'
import { RecurringPaymentsTable } from '@/components/recurring-payments/recurring-payments-table'
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Plus, Upload, Download } from 'lucide-react'

export default function RecurringPaymentsPage() {
  const [showForm, setShowForm] = useState(false)
  const [editingPayment, setEditingPayment] = useState<RecurringPayment | undefined>()
  const [refreshTrigger, setRefreshTrigger] = useState(0)

  const handleAddPayment = () => {
    setEditingPayment(undefined)
    setShowForm(true)
  }

  const handleEditPayment = (payment: RecurringPayment) => {
    setEditingPayment(payment)
    setShowForm(true)
  }

  const handleFormSuccess = () => {
    setShowForm(false)
    setEditingPayment(undefined)
    setRefreshTrigger(prev => prev + 1)
  }

  const handleFormCancel = () => {
    setShowForm(false)
    setEditingPayment(undefined)
  }

  return (
    <AppLayout>
      <div className="max-w-6xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Recurring Payments</h1>
            <p className="text-muted-foreground">
              Manage your subscriptions, bills, and recurring expenses
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline">
              <Upload className="w-4 h-4 mr-2" />
              Import
            </Button>
            <Button variant="outline">
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
            <Button onClick={handleAddPayment}>
              <Plus className="w-4 h-4 mr-2" />
              Add Payment
            </Button>
          </div>
        </div>

        {/* Recurring Payments Table */}
        <RecurringPaymentsTable
          onEditPayment={handleEditPayment}
          onAddPayment={handleAddPayment}
          refreshTrigger={refreshTrigger}
        />

        {/* Payment Form Dialog */}
        <Dialog open={showForm} onOpenChange={setShowForm}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>
                {editingPayment ? 'Edit Recurring Payment' : 'Add New Recurring Payment'}
              </DialogTitle>
            </DialogHeader>
            <RecurringPaymentForm
              payment={editingPayment}
              onSuccess={handleFormSuccess}
              onCancel={handleFormCancel}
            />
          </DialogContent>
        </Dialog>
      </div>
    </AppLayout>
  )
}
