'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/lib/contexts/auth-context'
import { getMonthlyFinancialSummary } from '@/lib/supabase/queries'
import { useDashboardData } from '@/lib/hooks/use-dashboard-data'
import { ErrorBoundary } from '@/components/error-boundary'
import { LoadingError } from '@/components/error/error-messages'
import { log } from '@/lib/utils/logger'
import { IconTrendingDown, IconTrendingUp, IconMinus } from "@tabler/icons-react"
import { Badge } from "@/components/ui/badge"
import {
  Card,
  CardAction,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"

interface FinancialSummary {
  income: number
  expenses: number
  balance: number
}

export function FinancialSummaryCards() {
  const { user, profile } = useAuth()
  const { data: dashboardData, loading, error, refetch } = useDashboardData()
  const [previousMonth, setPreviousMonth] = useState<FinancialSummary>({ income: 0, expenses: 0, balance: 0 })

  const currency = profile?.currency_code || 'USD'
  const currencySymbol = getCurrencySymbol(currency)

  // Get current month data from optimized dashboard hook
  const currentMonth = dashboardData?.financialSummary || { income: 0, expenses: 0, balance: 0 }

  useEffect(() => {
    if (user) {
      loadPreviousMonthData()
    }
  }, [user])

  const loadPreviousMonthData = async () => {
    if (!user) {
      log.warn('loadPreviousMonthData called without user', {}, 'FINANCIAL_SUMMARY')
      return
    }

    try {
      const now = new Date()
      const currentYear = now.getFullYear()
      const currentMonthNum = now.getMonth()
      const previousMonthNum = currentMonthNum === 0 ? 11 : currentMonthNum - 1
      const previousYear = currentMonthNum === 0 ? currentYear - 1 : currentYear

      log.debug('Loading previous month financial data', {
        userId: user.id,
        year: previousYear,
        month: previousMonthNum
      }, 'FINANCIAL_SUMMARY')

      const previous = await getMonthlyFinancialSummary(user.id, previousYear, previousMonthNum)
      setPreviousMonth(previous)

      log.info('Successfully loaded previous month data', {
        income: previous.income,
        expenses: previous.expenses,
        balance: previous.balance
      }, 'FINANCIAL_SUMMARY')
    } catch (error) {
      log.error('Error loading previous month data', {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        userId: user.id
      }, 'FINANCIAL_SUMMARY')

      // Set default values on error to prevent UI issues
      setPreviousMonth({ income: 0, expenses: 0, balance: 0 })
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2,
    }).format(amount)
  }

  const calculatePercentageChange = (current: number, previous: number) => {
    if (previous === 0) return current > 0 ? 100 : 0
    return ((current - previous) / previous) * 100
  }

  const getChangeIcon = (change: number) => {
    if (change > 0) return IconTrendingUp
    if (change < 0) return IconTrendingDown
    return IconMinus
  }

  const getChangeColor = (change: number, isIncome: boolean = false) => {
    if (change > 0) return isIncome ? 'text-green-600' : 'text-red-600'
    if (change < 0) return isIncome ? 'text-red-600' : 'text-green-600'
    return 'text-gray-600'
  }

  const incomeChange = calculatePercentageChange(currentMonth.income, previousMonth.income)
  const expenseChange = calculatePercentageChange(currentMonth.expenses, previousMonth.expenses)
  const balanceChange = calculatePercentageChange(Math.abs(currentMonth.balance), Math.abs(previousMonth.balance))

  const IncomeIcon = getChangeIcon(incomeChange)
  const ExpenseIcon = getChangeIcon(expenseChange)
  const BalanceIcon = getChangeIcon(balanceChange)

  if (loading) {
    return (
      <div className="grid grid-cols-1 gap-4 px-4 lg:px-6 @xl/main:grid-cols-2 @5xl/main:grid-cols-3">
        {[1, 2, 3].map((i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader>
              <CardDescription className="h-4 bg-gray-200 rounded w-24"></CardDescription>
              <CardTitle className="h-8 bg-gray-200 rounded w-32"></CardTitle>
            </CardHeader>
          </Card>
        ))}
      </div>
    )
  }

  if (error) {
    return (
      <div className="grid grid-cols-1 gap-4 px-4 lg:px-6 @xl/main:grid-cols-2 @5xl/main:grid-cols-3">
        <LoadingError
          resource="financial summary"
          onRetry={refetch}
          className="col-span-full"
        />
      </div>
    )
  }

  return (
    <ErrorBoundary>
    <div className="*:data-[slot=card]:from-primary/5 *:data-[slot=card]:to-card dark:*:data-[slot=card]:bg-card grid grid-cols-1 gap-4 px-4 *:data-[slot=card]:bg-gradient-to-t *:data-[slot=card]:shadow-xs lg:px-6 @xl/main:grid-cols-2 @5xl/main:grid-cols-3">
      {/* Monthly Income Card */}
      <Card className="@container/card">
        <CardHeader>
          <CardDescription>Monthly Income</CardDescription>
          <CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl text-green-600">
            {formatCurrency(currentMonth.income)}
          </CardTitle>
          <CardAction>
            <Badge variant="outline" className={getChangeColor(incomeChange, true)}>
              <IncomeIcon className="w-4 h-4" />
              {incomeChange > 0 ? '+' : ''}{incomeChange.toFixed(1)}%
            </Badge>
          </CardAction>
        </CardHeader>
        <CardFooter className="flex-col items-start gap-1.5 text-sm">
          <div className="line-clamp-1 flex gap-2 font-medium">
            {incomeChange > 0 ? 'Income increased' : incomeChange < 0 ? 'Income decreased' : 'Income unchanged'}
            <IncomeIcon className="size-4" />
          </div>
          <div className="text-muted-foreground">
            Compared to last month
          </div>
        </CardFooter>
      </Card>

      {/* Monthly Expenses Card */}
      <Card className="@container/card">
        <CardHeader>
          <CardDescription>Monthly Expenses</CardDescription>
          <CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl text-red-600">
            {formatCurrency(currentMonth.expenses)}
          </CardTitle>
          <CardAction>
            <Badge variant="outline" className={getChangeColor(expenseChange, false)}>
              <ExpenseIcon className="w-4 h-4" />
              {expenseChange > 0 ? '+' : ''}{expenseChange.toFixed(1)}%
            </Badge>
          </CardAction>
        </CardHeader>
        <CardFooter className="flex-col items-start gap-1.5 text-sm">
          <div className="line-clamp-1 flex gap-2 font-medium">
            {expenseChange > 0 ? 'Spending increased' : expenseChange < 0 ? 'Spending decreased' : 'Spending unchanged'}
            <ExpenseIcon className="size-4" />
          </div>
          <div className="text-muted-foreground">
            Compared to last month
          </div>
        </CardFooter>
      </Card>

      {/* Net Balance Card */}
      <Card className="@container/card">
        <CardHeader>
          <CardDescription>Net Balance</CardDescription>
          <CardTitle className={`text-2xl font-semibold tabular-nums @[250px]/card:text-3xl ${
            currentMonth.balance >= 0 ? 'text-blue-600' : 'text-orange-600'
          }`}>
            {formatCurrency(currentMonth.balance)}
          </CardTitle>
          <CardAction>
            <Badge variant="outline" className={currentMonth.balance >= 0 ? 'text-blue-600' : 'text-orange-600'}>
              <BalanceIcon className="w-4 h-4" />
              {currentMonth.balance >= 0 ? 'Positive' : 'Negative'}
            </Badge>
          </CardAction>
        </CardHeader>
        <CardFooter className="flex-col items-start gap-1.5 text-sm">
          <div className="line-clamp-1 flex gap-2 font-medium">
            {currentMonth.balance >= 0 ? 'Healthy financial position' : 'Spending exceeds income'}
            <BalanceIcon className="size-4" />
          </div>
          <div className="text-muted-foreground">
            Income minus expenses
          </div>
        </CardFooter>
      </Card>
    </div>
    </ErrorBoundary>
  )
}

function getCurrencySymbol(currencyCode: string): string {
  const symbols: Record<string, string> = {
    USD: '$',
    EUR: '€',
    GBP: '£',
    JPY: '¥',
    CAD: 'C$',
    AUD: 'A$',
    CHF: 'CHF',
    CNY: '¥',
    INR: '₹',
    KWD: 'د.ك',
    BRL: 'R$',
  }
  return symbols[currencyCode] || currencyCode
}
