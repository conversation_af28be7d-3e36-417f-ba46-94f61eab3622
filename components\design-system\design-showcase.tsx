'use client'

import React from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { 
  CheckCircle, 
  AlertTriangle, 
  XCircle, 
  Info,
  Palette,
  Type,
  Layout,
  Zap
} from 'lucide-react'

export function DesignShowcase() {
  return (
    <div className="min-h-screen bg-background p-8">
      <div className="max-w-7xl mx-auto space-y-12">
        
        {/* Header */}
        <div className="text-center space-y-4">
          <h1 className="text-display-large text-text-primary">
            Perfected Design System
          </h1>
          <p className="text-body-large text-text-secondary max-w-2xl mx-auto">
            A comprehensive design system with perfect typography, color harmony, 
            consistent spacing, and standardized components for exceptional user experience.
          </p>
        </div>

        {/* Typography Scale */}
        <Card className="card-base">
          <CardHeader className="card-header">
            <CardTitle className="flex items-center gap-3">
              <Type className="icon icon-lg text-primary" />
              Typography Scale (1.25 Ratio)
            </CardTitle>
          </CardHeader>
          <CardContent className="card-content space-y-6">
            <div className="grid gap-4">
              <div className="space-y-2">
                <div className="text-display-large">Display Large (46px)</div>
                <div className="text-label-small text-text-tertiary">
                  Font: Light 300 • Line Height: 1.2 • Letter Spacing: -0.02em
                </div>
              </div>
              
              <div className="space-y-2">
                <div className="text-display-medium">Display Medium (37px)</div>
                <div className="text-label-small text-text-tertiary">
                  Font: Regular 400 • Line Height: 1.2 • Letter Spacing: -0.02em
                </div>
              </div>
              
              <div className="space-y-2">
                <div className="text-heading-large">Heading Large (30px)</div>
                <div className="text-label-small text-text-tertiary">
                  Font: Semibold 600 • Line Height: 1.2 • Letter Spacing: 0
                </div>
              </div>
              
              <div className="space-y-2">
                <div className="text-heading-medium">Heading Medium (24px)</div>
                <div className="text-label-small text-text-tertiary">
                  Font: Semibold 600 • Line Height: 1.2 • Letter Spacing: 0
                </div>
              </div>
              
              <div className="space-y-2">
                <div className="text-body-large">Body Large (18px) - Primary content text</div>
                <div className="text-label-small text-text-tertiary">
                  Font: Regular 400 • Line Height: 1.5 • Letter Spacing: 0
                </div>
              </div>
              
              <div className="space-y-2">
                <div className="text-body-medium">Body Medium (15px) - Secondary content text</div>
                <div className="text-label-small text-text-tertiary">
                  Font: Regular 400 • Line Height: 1.5 • Letter Spacing: 0
                </div>
              </div>
              
              <div className="space-y-2">
                <div className="text-label-large">LABEL LARGE (15px)</div>
                <div className="text-label-small text-text-tertiary">
                  Font: Semibold 600 • Line Height: 1.4 • Letter Spacing: 0.05em
                </div>
              </div>
              
              <div className="space-y-2">
                <div className="text-label-small">LABEL SMALL (12px)</div>
                <div className="text-label-small text-text-tertiary">
                  Font: Semibold 600 • Line Height: 1.4 • Letter Spacing: 0.05em
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Color System */}
        <Card className="card-base">
          <CardHeader className="card-header">
            <CardTitle className="flex items-center gap-3">
              <Palette className="icon icon-lg text-primary" />
              WCAG AA Compliant Color System
            </CardTitle>
          </CardHeader>
          <CardContent className="card-content">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              
              {/* Primary Colors */}
              <div className="space-y-3">
                <h3 className="text-heading-medium">Primary</h3>
                <div className="space-y-2">
                  <div className="h-12 bg-primary-500 rounded-md flex items-center justify-center text-white text-label-large">
                    500 - Base
                  </div>
                  <div className="h-8 bg-primary-400 rounded-md flex items-center justify-center text-white text-label-small">
                    400 - Hover
                  </div>
                  <div className="h-8 bg-primary-600 rounded-md flex items-center justify-center text-white text-label-small">
                    600 - Active
                  </div>
                </div>
              </div>

              {/* Semantic Colors */}
              <div className="space-y-3">
                <h3 className="text-heading-medium">Semantic</h3>
                <div className="space-y-2">
                  <div className="h-8 bg-success-500 rounded-md flex items-center justify-center text-white text-label-small">
                    Success
                  </div>
                  <div className="h-8 bg-warning-500 rounded-md flex items-center justify-center text-neutral-900 text-label-small">
                    Warning
                  </div>
                  <div className="h-8 bg-error-500 rounded-md flex items-center justify-center text-white text-label-small">
                    Error
                  </div>
                  <div className="h-8 bg-info-500 rounded-md flex items-center justify-center text-white text-label-small">
                    Info
                  </div>
                </div>
              </div>

              {/* Text Colors */}
              <div className="space-y-3">
                <h3 className="text-heading-medium">Text</h3>
                <div className="space-y-2">
                  <div className="text-text-primary text-body-medium">Primary (15.3:1)</div>
                  <div className="text-text-secondary text-body-medium">Secondary (8.2:1)</div>
                  <div className="text-text-tertiary text-body-medium">Tertiary (5.9:1)</div>
                  <div className="text-text-disabled text-body-medium">Disabled (3.1:1)</div>
                </div>
              </div>

              {/* Neutral Colors */}
              <div className="space-y-3">
                <h3 className="text-heading-medium">Neutral</h3>
                <div className="grid grid-cols-5 gap-1">
                  <div className="h-8 bg-neutral-100 rounded border"></div>
                  <div className="h-8 bg-neutral-200 rounded border"></div>
                  <div className="h-8 bg-neutral-300 rounded border"></div>
                  <div className="h-8 bg-neutral-400 rounded border"></div>
                  <div className="h-8 bg-neutral-500 rounded border"></div>
                </div>
                <div className="text-label-small text-text-tertiary">100-500 Scale</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Component Standardization */}
        <Card className="card-base">
          <CardHeader className="card-header">
            <CardTitle className="flex items-center gap-3">
              <Layout className="icon icon-lg text-primary" />
              Standardized Components
            </CardTitle>
          </CardHeader>
          <CardContent className="card-content space-y-8">
            
            {/* Buttons */}
            <div className="space-y-4">
              <h3 className="text-heading-medium">Buttons (32px, 40px, 48px)</h3>
              <div className="flex flex-wrap items-center gap-4">
                <Button size="sm" className="btn-base btn-primary btn-sm">
                  Small Button
                </Button>
                <Button size="default" className="btn-base btn-primary btn-md">
                  Medium Button
                </Button>
                <Button size="lg" className="btn-base btn-primary btn-lg">
                  Large Button
                </Button>
              </div>
              
              <div className="flex flex-wrap items-center gap-4">
                <Button variant="secondary" size="default">
                  Secondary
                </Button>
                <Button variant="ghost" size="default">
                  Ghost
                </Button>
                <Button variant="destructive" size="default">
                  Destructive
                </Button>
              </div>
            </div>

            {/* Form Inputs */}
            <div className="space-y-4">
              <h3 className="text-heading-medium">Form Inputs</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 max-w-2xl">
                <Input 
                  placeholder="Small input" 
                  className="input-base input-sm h-8"
                />
                <Input 
                  placeholder="Medium input" 
                  className="input-base input-md"
                />
                <Input 
                  placeholder="Large input" 
                  className="input-base input-lg h-12"
                />
              </div>
            </div>

            {/* Status Badges */}
            <div className="space-y-4">
              <h3 className="text-heading-medium">Status Communication</h3>
              <div className="flex flex-wrap items-center gap-4">
                <Badge className="bg-success-500 text-white">
                  <CheckCircle className="icon icon-sm mr-1" />
                  Success
                </Badge>
                <Badge className="bg-warning-500 text-neutral-900">
                  <AlertTriangle className="icon icon-sm mr-1" />
                  Warning
                </Badge>
                <Badge className="bg-error-500 text-white">
                  <XCircle className="icon icon-sm mr-1" />
                  Error
                </Badge>
                <Badge className="bg-info-500 text-white">
                  <Info className="icon icon-sm mr-1" />
                  Info
                </Badge>
              </div>
            </div>

            {/* Icons */}
            <div className="space-y-4">
              <h3 className="text-heading-medium">Icon Sizing (16px, 20px, 24px, 32px)</h3>
              <div className="flex items-center gap-6">
                <div className="flex items-center gap-2">
                  <Zap className="icon icon-sm text-primary" />
                  <span className="text-label-small">16px</span>
                </div>
                <div className="flex items-center gap-2">
                  <Zap className="icon icon-md text-primary" />
                  <span className="text-label-small">20px</span>
                </div>
                <div className="flex items-center gap-2">
                  <Zap className="icon icon-lg text-primary" />
                  <span className="text-label-small">24px</span>
                </div>
                <div className="flex items-center gap-2">
                  <Zap className="icon icon-xl text-primary" />
                  <span className="text-label-small">32px</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Spacing System */}
        <Card className="card-base">
          <CardHeader className="card-header">
            <CardTitle className="flex items-center gap-3">
              <Layout className="icon icon-lg text-primary" />
              Spacing System (4px/8px Base)
            </CardTitle>
          </CardHeader>
          <CardContent className="card-content">
            <div className="space-y-4">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="space-y-2">
                  <div className="h-1 bg-primary-500 rounded" style={{width: '4px'}}></div>
                  <div className="text-label-small">4px (space-1)</div>
                </div>
                <div className="space-y-2">
                  <div className="h-1 bg-primary-500 rounded" style={{width: '8px'}}></div>
                  <div className="text-label-small">8px (space-2)</div>
                </div>
                <div className="space-y-2">
                  <div className="h-1 bg-primary-500 rounded" style={{width: '16px'}}></div>
                  <div className="text-label-small">16px (space-4)</div>
                </div>
                <div className="space-y-2">
                  <div className="h-1 bg-primary-500 rounded" style={{width: '24px'}}></div>
                  <div className="text-label-small">24px (space-6)</div>
                </div>
              </div>
              
              <div className="text-body-medium text-text-secondary">
                Consistent spacing creates visual rhythm and hierarchy. All components use multiples of 4px for perfect alignment.
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
