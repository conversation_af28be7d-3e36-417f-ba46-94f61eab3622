import { supabase } from './client'
import { UserProfile, Category, Transaction, RecurringPayment, Goal, Loan } from '@/lib/types/database'
import { log } from '@/lib/utils/logger'

// User Profile Operations
export const getUserProfile = async (userId: string) => {
  const { data, error } = await supabase
    .from('user_profiles')
    .select('*')
    .eq('id', userId)
    .single()
  
  if (error) throw error
  return data
}

export const createUserProfile = async (userId: string, profileData: Partial<UserProfile>) => {
  const { data, error } = await supabase
    .from('user_profiles')
    .insert({
      id: userId,
      full_name: null, // Explicitly set to null initially
      currency_code: 'USD',
      monthly_income: null,
      salary_payment_date: 1,
      ...profileData
    })
    .select()
    .single()

  if (error) throw error
  return data
}

export const updateUserProfile = async (userId: string, updates: Partial<UserProfile>) => {
  const { data, error } = await supabase
    .from('user_profiles')
    .update(updates)
    .eq('id', userId)
    .select()
    .single()

  if (error) throw error
  return data
}

export const upsertUserProfile = async (userId: string, profileData: Partial<UserProfile>) => {
  const { data, error } = await supabase
    .from('user_profiles')
    .upsert({
      id: userId,
      full_name: null, // Explicitly set to null initially
      currency_code: 'USD',
      monthly_income: null,
      salary_payment_date: 1,
      ...profileData
    })
    .select()
    .single()

  if (error) throw error
  return data
}

// Category Operations
export const getUserCategories = async (userId: string) => {
  const { data, error } = await supabase
    .from('categories')
    .select('*')
    .eq('user_id', userId)
    .order('name')
  
  if (error) throw error
  return data
}

export const createCategory = async (category: Omit<Category, 'id' | 'created_at' | 'updated_at'>) => {
  const { data, error } = await supabase
    .from('categories')
    .insert(category)
    .select()
    .single()
  
  if (error) throw error
  return data
}

// Transaction Operations
export const getUserTransactions = async (userId: string, limit?: number) => {
  try {
    log.debug('Fetching user transactions', { userId, limit }, 'SUPABASE_QUERIES')

    let query = supabase
      .from('transactions')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })

    if (limit) {
      query = query.limit(limit)
    }

    const { data, error } = await query

    if (error) {
      log.error('Supabase error in getUserTransactions', {
        error: error.message,
        code: error.code,
        details: error.details,
        hint: error.hint,
        userId
      }, 'SUPABASE_QUERIES')
      throw new Error(`Failed to fetch transactions: ${error.message}`)
    }

    log.info('Successfully fetched user transactions', {
      userId,
      count: data?.length || 0
    }, 'SUPABASE_QUERIES')

    return data || []
  } catch (err) {
    if (err instanceof Error && err.message.includes('Failed to fetch transactions')) {
      throw err
    }

    log.error('Unexpected error in getUserTransactions', {
      error: err instanceof Error ? err.message : String(err),
      userId
    }, 'SUPABASE_QUERIES')

    throw new Error('An unexpected error occurred while fetching transactions')
  }
}

export const createTransaction = async (transaction: Omit<Transaction, 'id' | 'created_at' | 'updated_at'>) => {
  const { data, error } = await supabase
    .from('transactions')
    .insert(transaction)
    .select('*')
    .single()

  if (error) throw error
  return data
}

export const updateTransaction = async (id: string, updates: Partial<Transaction>) => {
  const { data, error } = await supabase
    .from('transactions')
    .update(updates)
    .eq('id', id)
    .select('*')
    .single()

  if (error) throw error
  return data
}

export const deleteTransaction = async (id: string) => {
  const { error } = await supabase
    .from('transactions')
    .delete()
    .eq('id', id)
  
  if (error) throw error
}

// Recurring Payment Operations
export const getUserRecurringPayments = async (userId: string) => {
  const { data, error } = await supabase
    .from('recurring_payments')
    .select('*')
    .eq('user_id', userId)
    .order('created_at')

  if (error) throw error
  return data
}

export const createRecurringPayment = async (payment: Omit<RecurringPayment, 'id' | 'created_at' | 'updated_at'>) => {
  const { data, error } = await supabase
    .from('recurring_payments')
    .insert(payment)
    .select('*')
    .single()

  if (error) throw error
  return data
}

export const updateRecurringPayment = async (id: string, updates: Partial<RecurringPayment>) => {
  const { data, error } = await supabase
    .from('recurring_payments')
    .update(updates)
    .eq('id', id)
    .select('*')
    .single()

  if (error) throw error
  return data
}

export const deleteRecurringPayment = async (id: string) => {
  const { error } = await supabase
    .from('recurring_payments')
    .delete()
    .eq('id', id)

  if (error) throw error
}

// Goal Operations
export const getUserGoals = async (userId: string) => {
  const { data, error } = await supabase
    .from('goals')
    .select('*')
    .eq('user_id', userId)
    .order('created_at', { ascending: false })
  
  if (error) throw error
  return data
}

export const createGoal = async (goal: Omit<Goal, 'id' | 'created_at' | 'updated_at'>) => {
  const { data, error } = await supabase
    .from('goals')
    .insert(goal)
    .select()
    .single()
  
  if (error) throw error
  return data
}

export const updateGoal = async (id: string, updates: Partial<Goal>) => {
  const { data, error } = await supabase
    .from('goals')
    .update(updates)
    .eq('id', id)
    .select()
    .single()

  if (error) throw error
  return data
}

export const deleteGoal = async (id: string) => {
  const { error } = await supabase
    .from('goals')
    .delete()
    .eq('id', id)

  if (error) throw error
}

// Loan Operations
export const getUserLoans = async (userId: string) => {
  const { data, error } = await supabase
    .from('loans')
    .select('*')
    .eq('user_id', userId)
    .order('created_at', { ascending: false })
  
  if (error) throw error
  return data
}

export const createLoan = async (loan: Omit<Loan, 'id' | 'created_at' | 'updated_at'>) => {
  const { data, error } = await supabase
    .from('loans')
    .insert(loan)
    .select()
    .single()

  if (error) throw error
  return data
}

export const updateLoan = async (id: string, updates: Partial<Loan>) => {
  const { data, error } = await supabase
    .from('loans')
    .update(updates)
    .eq('id', id)
    .select()
    .single()

  if (error) throw error
  return data
}

export const deleteLoan = async (id: string) => {
  const { error } = await supabase
    .from('loans')
    .delete()
    .eq('id', id)

  if (error) throw error
}

// Financial Summary Operations
export const getMonthlyFinancialSummary = async (userId: string, year: number, month: number) => {
  try {
    const startDate = `${year}-${month.toString().padStart(2, '0')}-01`
    const endDate = `${year}-${(month + 1).toString().padStart(2, '0')}-01`

    log.debug('Fetching monthly financial summary', {
      userId,
      year,
      month,
      startDate,
      endDate
    }, 'SUPABASE_QUERIES')

    const { data, error } = await supabase
      .from('transactions')
      .select('type, amount')
      .eq('user_id', userId)
      .gte('created_at', startDate)
      .lt('created_at', endDate)

    if (error) {
      log.error('Supabase error in getMonthlyFinancialSummary', {
        error: error.message,
        code: error.code,
        details: error.details,
        hint: error.hint,
        userId,
        year,
        month
      }, 'SUPABASE_QUERIES')
      throw new Error(`Failed to fetch financial summary: ${error.message}`)
    }

    const summary = (data || []).reduce((acc, transaction) => {
      if (transaction.type === 'income') {
        acc.income += transaction.amount
      } else {
        acc.expenses += Math.abs(transaction.amount)
      }
      return acc
    }, { income: 0, expenses: 0 })

    const result = {
      ...summary,
      balance: summary.income - summary.expenses
    }

    log.info('Successfully calculated monthly financial summary', {
      userId,
      year,
      month,
      transactionCount: data?.length || 0,
      ...result
    }, 'SUPABASE_QUERIES')

    return result
  } catch (err) {
    if (err instanceof Error && err.message.includes('Failed to fetch financial summary')) {
      throw err
    }

    log.error('Unexpected error in getMonthlyFinancialSummary', {
      error: err instanceof Error ? err.message : String(err),
      userId,
      year,
      month
    }, 'SUPABASE_QUERIES')

    throw new Error('An unexpected error occurred while calculating financial summary')
  }
}
