/**
 * Simple environment variable checker for Next.js
 * Handles both client and server-side environment variables properly
 */

import { log } from '@/lib/utils/logger'

/**
 * Check if required environment variables are present
 * This should be called in API routes or server-side code
 */
export function checkServerEnvironment(): { valid: boolean; missing: string[] } {
  const required = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY'
  ]
  
  const missing: string[] = []
  
  for (const varName of required) {
    if (!process.env[varName]) {
      missing.push(varName)
    }
  }
  
  return {
    valid: missing.length === 0,
    missing
  }
}

/**
 * Check if client-side environment variables are present
 * This can be called in client components
 */
export function checkClientEnvironment(): { valid: boolean; missing: string[] } {
  const required = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY'
  ]
  
  const missing: string[] = []
  
  for (const varName of required) {
    if (!process.env[varName]) {
      missing.push(varName)
    }
  }
  
  return {
    valid: missing.length === 0,
    missing
  }
}

/**
 * Get environment variable with fallback
 */
export function getEnv(key: string, fallback?: string): string {
  const value = process.env[key]
  if (!value && fallback === undefined) {
    log.warn(`Environment variable ${key} is not set`, { key }, 'ENV')
    return ''
  }
  return value || fallback || ''
}

/**
 * Check if we're in development mode
 */
export function isDev(): boolean {
  return process.env.NODE_ENV === 'development'
}

/**
 * Check if we're in production mode
 */
export function isProd(): boolean {
  return process.env.NODE_ENV === 'production'
}

/**
 * Get the base URL for the application
 */
export function getBaseUrl(): string {
  if (typeof window !== 'undefined') {
    // Client-side
    return window.location.origin
  }
  
  // Server-side
  if (process.env.VERCEL_URL) {
    return `https://${process.env.VERCEL_URL}`
  }
  
  if (process.env.NEXTAUTH_URL) {
    return process.env.NEXTAUTH_URL
  }
  
  return 'http://localhost:3000'
}
