@import "tailwindcss";
@import "tw-animate-css";
@import "./design-system.css";
@import "./premium-interactions.css";
@import "./advanced-visual-effects.css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  /* Design System Tokens */

  /* Spacing Scale */
  --spacing-0: 0;
  --spacing-1: 0.25rem;    /* 4px */
  --spacing-2: 0.5rem;     /* 8px */
  --spacing-3: 0.75rem;    /* 12px */
  --spacing-4: 1rem;       /* 16px */
  --spacing-5: 1.25rem;    /* 20px */
  --spacing-6: 1.5rem;     /* 24px */
  --spacing-8: 2rem;       /* 32px */
  --spacing-10: 2.5rem;    /* 40px */
  --spacing-12: 3rem;      /* 48px */
  --spacing-16: 4rem;      /* 64px */
  --spacing-20: 5rem;      /* 80px */
  --spacing-24: 6rem;      /* 96px */

  /* Typography Scale */
  --font-size-xs: 0.75rem;     /* 12px */
  --font-size-sm: 0.875rem;    /* 14px */
  --font-size-base: 1rem;      /* 16px */
  --font-size-lg: 1.125rem;    /* 18px */
  --font-size-xl: 1.25rem;     /* 20px */
  --font-size-2xl: 1.5rem;     /* 24px */
  --font-size-3xl: 1.875rem;   /* 30px */
  --font-size-4xl: 2.25rem;    /* 36px */
  --font-size-5xl: 3rem;       /* 48px */

  /* Line Heights */
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;

  /* Font Weights */
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* Border Radius */
  --radius-none: 0;
  --radius-sm: 0.25rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  --radius-full: 9999px;

  /* Default radius for components */
  --radius: var(--radius-lg);

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  /* Color System - Light Theme */
  --background: oklch(1 0 0);
  --foreground: oklch(0.141 0.005 285.823);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.141 0.005 285.823);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.141 0.005 285.823);
  --primary: oklch(0.705 0.213 47.604);
  --primary-foreground: oklch(0.98 0.016 73.684);
  --secondary: oklch(0.967 0.001 286.375);
  --secondary-foreground: oklch(0.21 0.006 285.885);
  --muted: oklch(0.967 0.001 286.375);
  --muted-foreground: oklch(0.552 0.016 285.938);
  --accent: oklch(0.967 0.001 286.375);
  --accent-foreground: oklch(0.21 0.006 285.885);
  --destructive: oklch(0.577 0.245 27.325);
  --destructive-foreground: oklch(0.98 0.016 73.684);
  --success: oklch(0.646 0.222 142.495);
  --success-foreground: oklch(0.98 0.016 73.684);
  --warning: oklch(0.828 0.189 84.429);
  --warning-foreground: oklch(0.141 0.005 285.823);
  --info: oklch(0.6 0.118 184.704);
  --info-foreground: oklch(0.98 0.016 73.684);
  --border: oklch(0.92 0.004 286.32);
  --input: oklch(0.92 0.004 286.32);
  --ring: oklch(0.705 0.213 47.604);

  /* Chart Colors */
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);

  /* Sidebar Colors */
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.141 0.005 285.823);
  --sidebar-primary: oklch(0.705 0.213 47.604);
  --sidebar-primary-foreground: oklch(0.98 0.016 73.684);
  --sidebar-accent: oklch(0.967 0.001 286.375);
  --sidebar-accent-foreground: oklch(0.21 0.006 285.885);
  --sidebar-border: oklch(0.92 0.004 286.32);
  --sidebar-ring: oklch(0.705 0.213 47.604);
}

.dark {
  /* Dark Theme Color Overrides */
  --background: oklch(0.141 0.005 285.823);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.21 0.006 285.885);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.21 0.006 285.885);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.646 0.222 41.116);
  --primary-foreground: oklch(0.98 0.016 73.684);
  --secondary: oklch(0.274 0.006 286.033);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.274 0.006 286.033);
  --muted-foreground: oklch(0.705 0.015 286.067);
  --accent: oklch(0.274 0.006 286.033);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --destructive-foreground: oklch(0.98 0.016 73.684);
  --success: oklch(0.646 0.222 142.495);
  --success-foreground: oklch(0.141 0.005 285.823);
  --warning: oklch(0.828 0.189 84.429);
  --warning-foreground: oklch(0.141 0.005 285.823);
  --info: oklch(0.6 0.118 184.704);
  --info-foreground: oklch(0.985 0 0);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.646 0.222 41.116);

  /* Dark Theme Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.3), 0 2px 4px -2px rgb(0 0 0 / 0.3);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.3), 0 4px 6px -4px rgb(0 0 0 / 0.3);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.3), 0 8px 10px -6px rgb(0 0 0 / 0.3);

  /* Dark Theme Chart Colors */
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);

  /* Dark Theme Sidebar Colors */
  --sidebar: oklch(0.21 0.006 285.885);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.646 0.222 41.116);
  --sidebar-primary-foreground: oklch(0.98 0.016 73.684);
  --sidebar-accent: oklch(0.274 0.006 286.033);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.646 0.222 41.116);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }

  /* Ensure minimum touch target size for mobile */
  button,
  [role="button"],
  input[type="button"],
  input[type="submit"],
  input[type="reset"] {
    min-height: 44px;
    min-width: 44px;
  }

  /* Improve text readability on mobile */
  @media (max-width: 768px) {
    body {
      font-size: 16px; /* Prevent zoom on iOS */
    }

    input,
    textarea,
    select {
      font-size: 16px; /* Prevent zoom on iOS */
    }
  }

  /* Improve focus visibility */
  :focus-visible {
    outline: 2px solid oklch(var(--ring));
    outline-offset: 2px;
  }

  /* Reduce motion for users who prefer it */
  @media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
      scroll-behavior: auto !important;
    }
  }
}

/* Design System Utility Classes */
@layer utilities {
  /* Typography Utilities */
  .text-xs { font-size: var(--font-size-xs); }
  .text-sm { font-size: var(--font-size-sm); }
  .text-base { font-size: var(--font-size-base); }
  .text-lg { font-size: var(--font-size-lg); }
  .text-xl { font-size: var(--font-size-xl); }
  .text-2xl { font-size: var(--font-size-2xl); }
  .text-3xl { font-size: var(--font-size-3xl); }
  .text-4xl { font-size: var(--font-size-4xl); }
  .text-5xl { font-size: var(--font-size-5xl); }

  .font-normal { font-weight: var(--font-weight-normal); }
  .font-medium { font-weight: var(--font-weight-medium); }
  .font-semibold { font-weight: var(--font-weight-semibold); }
  .font-bold { font-weight: var(--font-weight-bold); }

  .leading-tight { line-height: var(--line-height-tight); }
  .leading-normal { line-height: var(--line-height-normal); }
  .leading-relaxed { line-height: var(--line-height-relaxed); }

  /* Spacing Utilities */
  .space-1 { gap: var(--spacing-1); }
  .space-2 { gap: var(--spacing-2); }
  .space-3 { gap: var(--spacing-3); }
  .space-4 { gap: var(--spacing-4); }
  .space-5 { gap: var(--spacing-5); }
  .space-6 { gap: var(--spacing-6); }
  .space-8 { gap: var(--spacing-8); }
  .space-10 { gap: var(--spacing-10); }
  .space-12 { gap: var(--spacing-12); }

  /* Shadow Utilities */
  .shadow-sm { box-shadow: var(--shadow-sm); }
  .shadow-md { box-shadow: var(--shadow-md); }
  .shadow-lg { box-shadow: var(--shadow-lg); }
  .shadow-xl { box-shadow: var(--shadow-xl); }

  /* Border Radius Utilities */
  .rounded-sm { border-radius: var(--radius-sm); }
  .rounded-md { border-radius: var(--radius-md); }
  .rounded-lg { border-radius: var(--radius-lg); }
  .rounded-xl { border-radius: var(--radius-xl); }
  .rounded-2xl { border-radius: var(--radius-2xl); }
  .rounded-full { border-radius: var(--radius-full); }

  /* Semantic Color Utilities */
  .text-success { color: oklch(var(--success)); }
  .text-warning { color: oklch(var(--warning)); }
  .text-info { color: oklch(var(--info)); }
  .bg-success { background-color: oklch(var(--success)); }
  .bg-warning { background-color: oklch(var(--warning)); }
  .bg-info { background-color: oklch(var(--info)); }
  .border-success { border-color: oklch(var(--success)); }
  .border-warning { border-color: oklch(var(--warning)); }
  .border-info { border-color: oklch(var(--info)); }
}

/* Fix for dropdown transparency issues */
[data-slot="select-content"] {
  background-color: oklch(var(--popover)) !important;
  border: 1px solid oklch(var(--border));
}

[data-slot="select-item"] {
  background-color: transparent;
}

[data-slot="select-item"]:hover,
[data-slot="select-item"]:focus {
  background-color: oklch(var(--accent)) !important;
  color: oklch(var(--accent-foreground)) !important;
}

/* Fix sidebar transparency and z-index issues */
[data-slot="sidebar"] {
  background-color: oklch(var(--sidebar)) !important;
  opacity: 1 !important;
}

[data-slot="sidebar-inner"] {
  background-color: oklch(var(--sidebar)) !important;
  opacity: 1 !important;
}

/* Ensure proper z-index layering */
[data-slot="sidebar-container"] {
  z-index: 40 !important;
}

[data-slot="sheet-overlay"] {
  z-index: 45 !important;
}

[data-slot="sheet-content"] {
  z-index: 50 !important;
  background-color: oklch(var(--sidebar)) !important;
}

/* Fix mobile sidebar background */
[data-mobile="true"][data-slot="sidebar"] {
  background-color: oklch(var(--sidebar)) !important;
}

/* Ensure sidebar border is visible */
[data-side="left"][data-slot="sidebar-container"] {
  border-right: 1px solid oklch(var(--sidebar-border)) !important;
}

[data-side="right"][data-slot="sidebar-container"] {
  border-left: 1px solid oklch(var(--sidebar-border)) !important;
}

/* Fix sidebar wrapper background for inset variant */
[data-slot="sidebar-wrapper"][data-variant="inset"] {
  background-color: oklch(var(--sidebar)) !important;
}

/* Ensure proper backdrop for sheet overlay */
[data-slot="sheet-overlay"] {
  backdrop-filter: blur(2px);
  background-color: rgba(0, 0, 0, 0.6) !important;
}

/* Fix any remaining transparency issues */
.bg-sidebar {
  background-color: oklch(var(--sidebar)) !important;
}

.text-sidebar-foreground {
  color: oklch(var(--sidebar-foreground)) !important;
}

/* Ensure sidebar links are clickable */
[data-slot="sidebar-menu-button"] {
  pointer-events: auto !important;
  cursor: pointer !important;
}

[data-slot="sidebar-menu-button"] a {
  display: flex !important;
  align-items: center !important;
  gap: 0.5rem !important;
  width: 100% !important;
  pointer-events: auto !important;
  text-decoration: none !important;
}

/* Ensure proper z-index for clickable elements */
[data-slot="sidebar-menu-button"],
[data-slot="sidebar-menu-button"] a {
  position: relative !important;
  z-index: 1 !important;
}

/* Active sidebar menu item styling with primary orange color */
[data-slot="sidebar-menu-button"][data-active="true"] {
  background-color: oklch(var(--primary)) !important;
  color: oklch(var(--primary-foreground)) !important;
  font-weight: 600 !important;
  border-left: 3px solid oklch(var(--primary-foreground)) !important;
  position: relative !important;
}

[data-slot="sidebar-menu-button"][data-active="true"]:hover {
  background-color: oklch(var(--primary)) !important;
  color: oklch(var(--primary-foreground)) !important;
}

[data-slot="sidebar-menu-button"][data-active="true"] svg {
  color: oklch(var(--primary-foreground)) !important;
}

/* Enhanced active state for better visibility */
[data-slot="sidebar-menu-button"][data-active="true"]::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background-color: oklch(var(--primary-foreground));
  border-radius: 0 4px 4px 0;
}

/* Dark mode adjustments for active state */
.dark [data-slot="sidebar-menu-button"][data-active="true"] {
  background-color: oklch(var(--primary)) !important;
  color: oklch(var(--primary-foreground)) !important;
}

.dark [data-slot="sidebar-menu-button"][data-active="true"]::before {
  background-color: oklch(var(--primary-foreground)) !important;
}

/* Smooth transitions for state changes */
[data-slot="sidebar-menu-button"] {
  transition: all 200ms ease-in-out !important;
}

[data-slot="sidebar-menu-button"]::before {
  transition: all 200ms ease-in-out !important;
}
