import * as React from "react"
import { cn } from "@/lib/utils"

interface ResponsiveContainerProps extends React.HTMLAttributes<HTMLDivElement> {
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full'
  padding?: 'none' | 'sm' | 'md' | 'lg'
  center?: boolean
}

export function ResponsiveContainer({
  className,
  maxWidth = 'lg',
  padding = 'md',
  center = true,
  children,
  ...props
}: ResponsiveContainerProps) {
  const maxWidthClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl',
    '2xl': 'max-w-2xl',
    full: 'max-w-full'
  }

  const paddingClasses = {
    none: '',
    sm: 'px-4 py-2',
    md: 'px-4 py-4 sm:px-6 sm:py-6',
    lg: 'px-6 py-6 sm:px-8 sm:py-8'
  }

  return (
    <div
      className={cn(
        'w-full',
        maxWidthClasses[maxWidth],
        paddingClasses[padding],
        center && 'mx-auto',
        className
      )}
      {...props}
    >
      {children}
    </div>
  )
}

interface MobileOptimizedCardProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'default' | 'elevated' | 'outlined'
}

export function MobileOptimizedCard({
  className,
  variant = 'default',
  children,
  ...props
}: MobileOptimizedCardProps) {
  const variantClasses = {
    default: 'bg-card text-card-foreground border border-border',
    elevated: 'bg-card text-card-foreground shadow-lg border border-border',
    outlined: 'bg-transparent border-2 border-border'
  }

  return (
    <div
      className={cn(
        'rounded-lg',
        // Mobile-first responsive design
        'w-full max-w-sm mx-auto',
        'sm:max-w-md',
        'md:max-w-lg',
        // Touch-friendly spacing
        'p-4 sm:p-6',
        // Improved mobile shadows and borders
        variantClasses[variant],
        className
      )}
      {...props}
    >
      {children}
    </div>
  )
}

interface TouchFriendlyButtonGroupProps extends React.HTMLAttributes<HTMLDivElement> {
  orientation?: 'horizontal' | 'vertical'
  spacing?: 'sm' | 'md' | 'lg'
}

export function TouchFriendlyButtonGroup({
  className,
  orientation = 'horizontal',
  spacing = 'md',
  children,
  ...props
}: TouchFriendlyButtonGroupProps) {
  const orientationClasses = {
    horizontal: 'flex flex-row',
    vertical: 'flex flex-col'
  }

  const spacingClasses = {
    sm: orientation === 'horizontal' ? 'gap-2' : 'gap-2',
    md: orientation === 'horizontal' ? 'gap-3 sm:gap-4' : 'gap-3 sm:gap-4',
    lg: orientation === 'horizontal' ? 'gap-4 sm:gap-6' : 'gap-4 sm:gap-6'
  }

  return (
    <div
      className={cn(
        orientationClasses[orientation],
        spacingClasses[spacing],
        // Ensure buttons are touch-friendly on mobile
        '[&>button]:min-h-[44px] [&>button]:min-w-[44px]',
        'sm:[&>button]:min-h-[36px] sm:[&>button]:min-w-[36px]',
        className
      )}
      {...props}
    >
      {children}
    </div>
  )
}

interface MobileMenuProps extends React.HTMLAttributes<HTMLDivElement> {
  isOpen: boolean
  onClose: () => void
}

export function MobileMenu({
  className,
  isOpen,
  onClose,
  children,
  ...props
}: MobileMenuProps) {
  React.useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = 'unset'
    }

    return () => {
      document.body.style.overflow = 'unset'
    }
  }, [isOpen])

  if (!isOpen) return null

  return (
    <>
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black/50 z-40 md:hidden"
        onClick={onClose}
        aria-hidden="true"
      />
      
      {/* Menu */}
      <div
        className={cn(
          'fixed inset-x-0 top-0 z-50 bg-background border-b border-border',
          'transform transition-transform duration-300 ease-in-out',
          'md:hidden',
          isOpen ? 'translate-y-0' : '-translate-y-full',
          className
        )}
        {...props}
      >
        <div className="p-4 max-h-screen overflow-y-auto">
          {children}
        </div>
      </div>
    </>
  )
}

// Hook for detecting mobile devices
export function useIsMobile() {
  const [isMobile, setIsMobile] = React.useState(false)

  React.useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }

    checkIsMobile()
    window.addEventListener('resize', checkIsMobile)

    return () => {
      window.removeEventListener('resize', checkIsMobile)
    }
  }, [])

  return isMobile
}

// Hook for safe area insets (for devices with notches)
export function useSafeAreaInsets() {
  const [insets, setInsets] = React.useState({
    top: 0,
    right: 0,
    bottom: 0,
    left: 0
  })

  React.useEffect(() => {
    const updateInsets = () => {
      const style = getComputedStyle(document.documentElement)
      setInsets({
        top: parseInt(style.getPropertyValue('env(safe-area-inset-top)') || '0'),
        right: parseInt(style.getPropertyValue('env(safe-area-inset-right)') || '0'),
        bottom: parseInt(style.getPropertyValue('env(safe-area-inset-bottom)') || '0'),
        left: parseInt(style.getPropertyValue('env(safe-area-inset-left)') || '0')
      })
    }

    updateInsets()
    window.addEventListener('resize', updateInsets)
    window.addEventListener('orientationchange', updateInsets)

    return () => {
      window.removeEventListener('resize', updateInsets)
      window.removeEventListener('orientationchange', updateInsets)
    }
  }, [])

  return insets
}
