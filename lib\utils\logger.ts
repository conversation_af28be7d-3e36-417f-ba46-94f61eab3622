/**
 * Centralized logging utility
 * Provides structured logging with different levels and proper production handling
 */

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3
}

interface LogEntry {
  level: LogLevel
  message: string
  data?: any
  timestamp: string
  context?: string
}

class Logger {
  private isDevelopment = process.env.NODE_ENV === 'development'
  private isClient = typeof window !== 'undefined'

  private formatMessage(level: LogLevel, message: string, data?: any, context?: string): LogEntry {
    return {
      level,
      message,
      data,
      timestamp: new Date().toISOString(),
      context
    }
  }

  private shouldLog(level: LogLevel): boolean {
    // In production, only log warnings and errors
    if (!this.isDevelopment && level < LogLevel.WARN) {
      return false
    }
    return true
  }

  private logToConsole(entry: LogEntry): void {
    if (!this.shouldLog(entry.level)) return

    const prefix = entry.context ? `[${entry.context}]` : ''
    const timestamp = this.isDevelopment ? entry.timestamp : ''
    
    switch (entry.level) {
      case LogLevel.DEBUG:
        console.debug(`🐛 ${timestamp} ${prefix} ${entry.message}`, entry.data || '')
        break
      case LogLevel.INFO:
        console.info(`ℹ️ ${timestamp} ${prefix} ${entry.message}`, entry.data || '')
        break
      case LogLevel.WARN:
        console.warn(`⚠️ ${timestamp} ${prefix} ${entry.message}`, entry.data || '')
        break
      case LogLevel.ERROR:
        console.error(`❌ ${timestamp} ${prefix} ${entry.message}`, entry.data || '')
        break
    }
  }

  private logToService(entry: LogEntry): void {
    // In production, send logs to external service
    if (process.env.NODE_ENV === 'production' && entry.level >= LogLevel.WARN) {
      // Example: Send to Sentry, LogRocket, or other logging service
      // this.sendToExternalService(entry)
    }
  }

  debug(message: string, data?: any, context?: string): void {
    const entry = this.formatMessage(LogLevel.DEBUG, message, data, context)
    this.logToConsole(entry)
  }

  info(message: string, data?: any, context?: string): void {
    const entry = this.formatMessage(LogLevel.INFO, message, data, context)
    this.logToConsole(entry)
    this.logToService(entry)
  }

  warn(message: string, data?: any, context?: string): void {
    const entry = this.formatMessage(LogLevel.WARN, message, data, context)
    this.logToConsole(entry)
    this.logToService(entry)
  }

  error(message: string, data?: any, context?: string): void {
    const entry = this.formatMessage(LogLevel.ERROR, message, data, context)
    this.logToConsole(entry)
    this.logToService(entry)
  }

  // Specialized logging methods
  apiCall(method: string, url: string, data?: any): void {
    this.debug(`API ${method} ${url}`, data, 'API')
  }

  apiResponse(method: string, url: string, status: number, data?: any): void {
    if (status >= 400) {
      this.error(`API ${method} ${url} failed with status ${status}`, data, 'API')
    } else {
      this.debug(`API ${method} ${url} succeeded with status ${status}`, data, 'API')
    }
  }

  userAction(action: string, data?: any): void {
    this.info(`User action: ${action}`, data, 'USER')
  }

  performance(operation: string, duration: number, data?: any): void {
    if (duration > 1000) {
      this.warn(`Slow operation: ${operation} took ${duration}ms`, data, 'PERF')
    } else {
      this.debug(`Performance: ${operation} took ${duration}ms`, data, 'PERF')
    }
  }

  errorBoundary(error: Error, errorInfo?: any): void {
    this.error('Error boundary caught error', {
      error: error.toString(),
      stack: error.stack,
      errorInfo
    }, 'ERROR_BOUNDARY')
  }

  // Development-only logging
  dev(message: string, data?: any, context?: string): void {
    if (this.isDevelopment) {
      this.debug(message, data, context)
    }
  }
}

// Create singleton instance
export const logger = new Logger()

// Convenience exports
export const log = {
  debug: logger.debug.bind(logger),
  info: logger.info.bind(logger),
  warn: logger.warn.bind(logger),
  error: logger.error.bind(logger),
  dev: logger.dev.bind(logger),
  api: {
    call: logger.apiCall.bind(logger),
    response: logger.apiResponse.bind(logger)
  },
  user: logger.userAction.bind(logger),
  perf: logger.performance.bind(logger),
  errorBoundary: logger.errorBoundary.bind(logger)
}

// Legacy console.log replacement for gradual migration
export function devLog(...args: any[]): void {
  if (process.env.NODE_ENV === 'development') {
    console.log(...args)
  }
}

// Error reporting utility
export function reportError(error: Error, context?: string, additionalData?: any): void {
  logger.error(`Unhandled error: ${error.message}`, {
    error: error.toString(),
    stack: error.stack,
    context,
    additionalData
  }, 'ERROR_REPORT')
}

export default logger
