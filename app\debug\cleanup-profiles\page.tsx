'use client'

import { useState } from 'react'
import { useAuth } from '@/lib/contexts/auth-context'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, RefreshCw, CheckCircle } from 'lucide-react'

export default function CleanupProfilesPage() {
  const { user, profile, updateProfile, refreshProfile } = useAuth()
  const [loading, setLoading] = useState(false)
  const [message, setMessage] = useState('')
  const [error, setError] = useState('')

  const cleanupProfile = async () => {
    if (!user || !profile) {
      setError('No user or profile found')
      return
    }

    setLoading(true)
    setError('')
    setMessage('')

    try {
      // Check if full_name is an email address
      const needsCleanup = profile.full_name === user.email || 
                          (profile.full_name && profile.full_name.includes('@'))

      if (needsCleanup) {
        console.log('Cleaning up profile - full_name was:', profile.full_name)
        
        // Update profile to clear the email from full_name
        await updateProfile({
          full_name: null
        })

        await refreshProfile()
        setMessage('Profile cleaned up successfully! The full name field has been cleared.')
      } else {
        setMessage('Profile is already clean - no cleanup needed.')
      }
    } catch (err) {
      console.error('Error cleaning up profile:', err)
      setError('Failed to cleanup profile. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const refreshData = async () => {
    setLoading(true)
    setError('')
    setMessage('')

    try {
      await refreshProfile()
      setMessage('Profile data refreshed successfully!')
    } catch (err) {
      console.error('Error refreshing profile:', err)
      setError('Failed to refresh profile data.')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container mx-auto py-8 max-w-2xl">
      <div className="mb-8 text-center">
        <h1 className="text-3xl font-bold mb-2">Profile Cleanup Tool</h1>
        <p className="text-gray-600">
          This tool helps fix profiles where the full name field contains an email address.
        </p>
      </div>

      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Current Profile Data</CardTitle>
          <CardDescription>
            Review your current profile information
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium text-gray-500">User Email</label>
              <p className="text-sm bg-gray-50 p-2 rounded">{user?.email || 'Not available'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Profile Full Name</label>
              <p className="text-sm bg-gray-50 p-2 rounded">
                {profile?.full_name || 'Not set'}
              </p>
            </div>
          </div>

          {profile?.full_name === user?.email && (
            <Alert variant="destructive">
              <AlertDescription>
                ⚠️ Issue detected: Full name is the same as email address
              </AlertDescription>
            </Alert>
          )}

          {profile?.full_name && profile.full_name.includes('@') && profile.full_name !== user?.email && (
            <Alert variant="destructive">
              <AlertDescription>
                ⚠️ Issue detected: Full name appears to be an email address
              </AlertDescription>
            </Alert>
          )}

          {profile?.full_name && !profile.full_name.includes('@') && (
            <Alert>
              <CheckCircle className="w-4 h-4" />
              <AlertDescription>
                ✅ Profile looks good - full name is not an email address
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Cleanup Actions</CardTitle>
          <CardDescription>
            Use these tools to fix profile issues
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {message && (
            <Alert>
              <AlertDescription>{message}</AlertDescription>
            </Alert>
          )}

          <div className="flex gap-4">
            <Button 
              onClick={cleanupProfile} 
              disabled={loading}
              className="flex-1"
            >
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Cleaning...
                </>
              ) : (
                'Cleanup Profile'
              )}
            </Button>

            <Button 
              onClick={refreshData} 
              disabled={loading}
              variant="outline"
              className="flex-1"
            >
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Refreshing...
                </>
              ) : (
                <>
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Refresh Data
                </>
              )}
            </Button>
          </div>

          <div className="text-sm text-gray-500 space-y-2">
            <p><strong>Cleanup Profile:</strong> Clears the full name field if it contains an email address</p>
            <p><strong>Refresh Data:</strong> Reloads your profile data from the database</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
