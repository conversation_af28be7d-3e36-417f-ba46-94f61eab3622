'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/lib/contexts/auth-context'
import { createTransaction, updateTransaction, getUserCategories } from '@/lib/supabase/queries'
import { Transaction, Category } from '@/lib/types/database'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { CalendarIcon, DollarSign, Loader2 } from 'lucide-react'
import { format } from 'date-fns'

interface TransactionFormProps {
  transaction?: Transaction
  onSuccess: () => void
  onCancel: () => void
}

export function TransactionForm({ transaction, onSuccess, onCancel }: TransactionFormProps) {
  const { user, profile } = useAuth()
  const [categories, setCategories] = useState<Category[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  
  const [formData, setFormData] = useState({
    amount: transaction?.amount?.toString() || '',
    description: transaction?.description || '',
    category_id: transaction?.category_id || '',
    transaction_date: transaction?.transaction_date || format(new Date(), 'yyyy-MM-dd'),
    type: transaction?.type || 'expense' as 'income' | 'expense',
    notes: transaction?.notes || '',
    merchant_name: transaction?.merchant_name || ''
  })

  useEffect(() => {
    loadCategories()
  }, [user])

  const loadCategories = async () => {
    if (!user) return
    
    try {
      const userCategories = await getUserCategories(user.id)
      setCategories(userCategories)
    } catch (err) {
      console.error('Error loading categories:', err)
      setError('Failed to load categories')
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
    setError('')
  }

  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }))
    setError('')
  }

  const validateForm = () => {
    if (!formData.amount || isNaN(Number(formData.amount)) || Number(formData.amount) <= 0) {
      setError('Please enter a valid amount greater than 0')
      return false
    }

    if (!formData.description.trim()) {
      setError('Please enter a description')
      return false
    }

    if (!formData.category_id) {
      setError('Please select a category')
      return false
    }

    if (!formData.transaction_date) {
      setError('Please select a date')
      return false
    }

    return true
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm() || !user) return

    setLoading(true)
    setError('')

    try {
      const transactionData = {
        user_id: user.id,
        amount: Number(formData.amount),
        description: formData.description.trim(),
        category_id: formData.category_id || null,
        transaction_date: formData.transaction_date,
        type: formData.type,
        currency_code: profile?.currency_code || 'USD',
        notes: formData.notes.trim() || null,
        merchant_name: formData.merchant_name.trim() || null,
        receipt_url: null
      }

      if (transaction) {
        await updateTransaction(transaction.id, transactionData)
      } else {
        await createTransaction(transactionData)
      }
      
      onSuccess()
    } catch (err) {
      console.error('Error saving transaction:', err)

      // Provide specific error messages based on error type
      let errorMessage = 'Failed to save transaction. Please try again.'

      if (err instanceof Error) {
        if (err.message.includes('network') || err.message.includes('fetch')) {
          errorMessage = 'Network error: Unable to save transaction. Please check your internet connection and try again.'
        } else if (err.message.includes('duplicate') || err.message.includes('unique')) {
          errorMessage = 'This transaction may already exist. Please check your recent transactions.'
        } else if (err.message.includes('permission') || err.message.includes('unauthorized')) {
          errorMessage = 'Permission denied: You may need to sign in again to save transactions.'
        } else if (err.message.includes('validation') || err.message.includes('invalid')) {
          errorMessage = 'Invalid data: Please check all fields and ensure they contain valid information.'
        } else if (err.message.includes('timeout')) {
          errorMessage = 'Request timeout: The save operation took too long. Please try again.'
        } else if (err.message.trim()) {
          errorMessage = `Save failed: ${err.message}`
        }
      }

      setError(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  const incomeCategories = categories.filter(cat => 
    ['Salary', 'Freelance', 'Investment', 'Other Income'].includes(cat.name)
  )
  
  const expenseCategories = categories.filter(cat => 
    !['Salary', 'Freelance', 'Investment', 'Other Income'].includes(cat.name)
  )

  const availableCategories = formData.type === 'income' ? incomeCategories : expenseCategories

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <DollarSign className="w-5 h-5" />
          {transaction ? 'Edit Transaction' : 'Add New Transaction'}
        </CardTitle>
        <CardDescription>
          {transaction ? 'Update transaction details' : 'Enter the details for your new transaction'}
        </CardDescription>
      </CardHeader>

      <form onSubmit={handleSubmit} aria-label={transaction ? 'Edit transaction form' : 'Add new transaction form'}>
        <CardContent className="space-y-6">
          {error && (
            <Alert variant="destructive" role="alert" aria-live="polite">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Transaction Type */}
          <div className="space-y-2">
            <Label htmlFor="type">Transaction Type *</Label>
            <Select
              value={formData.type}
              onValueChange={(value) => handleSelectChange('type', value)}
              aria-label="Select transaction type"
              aria-describedby="type-hint"
            >
              <SelectTrigger>
                <SelectValue placeholder="Select transaction type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="income">Income</SelectItem>
                <SelectItem value="expense">Expense</SelectItem>
              </SelectContent>
            </Select>
            <div id="type-hint" className="text-sm text-gray-600">
              Choose whether this is money coming in (income) or going out (expense)
            </div>
          </div>

          {/* Amount */}
          <div className="space-y-2">
            <Label htmlFor="amount">Amount ({profile?.currency_code || 'USD'}) *</Label>
            <Input
              id="amount"
              name="amount"
              type="number"
              step="0.01"
              min="0"
              placeholder="0.00"
              value={formData.amount}
              onChange={handleInputChange}
              className="text-lg"
              aria-label={`Transaction amount in ${profile?.currency_code || 'USD'}`}
              aria-describedby="amount-hint"
              aria-required="true"
            />
            <div id="amount-hint" className="text-sm text-gray-600">
              Enter the amount for this transaction (positive numbers only)
            </div>
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description">Description *</Label>
            <Input
              id="description"
              name="description"
              placeholder="Enter transaction description"
              value={formData.description}
              onChange={handleInputChange}
              aria-label="Transaction description"
              aria-describedby="description-hint"
              aria-required="true"
            />
            <div id="description-hint" className="text-sm text-gray-600">
              Provide a brief description of what this transaction is for
            </div>
          </div>

          {/* Category */}
          <div className="space-y-2">
            <Label htmlFor="category_id">Category *</Label>
            <Select
              value={formData.category_id}
              onValueChange={(value) => handleSelectChange('category_id', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a category" />
              </SelectTrigger>
              <SelectContent>
                {availableCategories.map((category) => (
                  <SelectItem key={category.id} value={category.id}>
                    <div className="flex items-center space-x-2">
                      <div
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: category.color }}
                      />
                      <span>{category.name}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Date */}
          <div className="space-y-2">
            <Label htmlFor="transaction_date">Date *</Label>
            <Input
              id="transaction_date"
              name="transaction_date"
              type="date"
              value={formData.transaction_date}
              onChange={handleInputChange}
            />
          </div>

          {/* Notes */}
          <div className="space-y-2">
            <Label htmlFor="notes">Notes</Label>
            <Input
              id="notes"
              name="notes"
              placeholder="Optional notes"
              value={formData.notes}
              onChange={handleInputChange}
            />
          </div>

          {/* Merchant Name */}
          <div className="space-y-2">
            <Label htmlFor="merchant_name">Merchant</Label>
            <Input
              id="merchant_name"
              name="merchant_name"
              placeholder="Store or merchant name"
              value={formData.merchant_name}
              onChange={handleInputChange}
            />
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-3 pt-4">
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {transaction ? 'Update Transaction' : 'Add Transaction'}
            </Button>
          </div>
        </CardContent>
      </form>
    </Card>
  )
}
