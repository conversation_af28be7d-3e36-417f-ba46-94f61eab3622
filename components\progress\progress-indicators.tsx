'use client'

import React from 'react'
import { Check, ChevronRight } from 'lucide-react'
import { cn } from '@/lib/utils'

// Multi-step Progress Indicator
interface Step {
  id: string
  title: string
  description?: string
  status: 'pending' | 'current' | 'completed' | 'error'
}

interface MultiStepProgressProps {
  steps: Step[]
  currentStep: number
  className?: string
  variant?: 'horizontal' | 'vertical'
  showDescriptions?: boolean
}

export function MultiStepProgress({ 
  steps, 
  currentStep, 
  className,
  variant = 'horizontal',
  showDescriptions = false
}: MultiStepProgressProps) {
  if (variant === 'vertical') {
    return (
      <div className={cn("space-y-4", className)}>
        {steps.map((step, index) => {
          const isCompleted = index < currentStep
          const isCurrent = index === currentStep
          const isError = step.status === 'error'
          
          return (
            <div key={step.id} className="flex items-start gap-4">
              {/* Step Indicator */}
              <div className="flex flex-col items-center">
                <div
                  className={cn(
                    "w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-all duration-300",
                    isCompleted && "bg-success text-white",
                    isCurrent && "bg-primary text-white ring-4 ring-primary/20",
                    !isCompleted && !isCurrent && !isError && "bg-neutral-200 text-text-tertiary",
                    isError && "bg-error text-white"
                  )}
                >
                  {isCompleted ? (
                    <Check className="w-4 h-4" />
                  ) : (
                    <span>{index + 1}</span>
                  )}
                </div>
                
                {/* Connector Line */}
                {index < steps.length - 1 && (
                  <div
                    className={cn(
                      "w-0.5 h-8 mt-2 transition-colors duration-300",
                      isCompleted ? "bg-success" : "bg-neutral-200"
                    )}
                  />
                )}
              </div>
              
              {/* Step Content */}
              <div className="flex-1 pb-8">
                <h3
                  className={cn(
                    "font-medium transition-colors duration-300",
                    isCurrent && "text-primary",
                    isCompleted && "text-success",
                    !isCurrent && !isCompleted && "text-text-secondary"
                  )}
                >
                  {step.title}
                </h3>
                {showDescriptions && step.description && (
                  <p className="text-sm text-text-tertiary mt-1">
                    {step.description}
                  </p>
                )}
              </div>
            </div>
          )
        })}
      </div>
    )
  }
  
  // Horizontal variant
  return (
    <div className={cn("flex items-center justify-between", className)}>
      {steps.map((step, index) => {
        const isCompleted = index < currentStep
        const isCurrent = index === currentStep
        const isError = step.status === 'error'
        
        return (
          <React.Fragment key={step.id}>
            <div className="flex flex-col items-center">
              {/* Step Indicator */}
              <div
                className={cn(
                  "w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium transition-all duration-300 mb-2",
                  isCompleted && "bg-success text-white",
                  isCurrent && "bg-primary text-white ring-4 ring-primary/20",
                  !isCompleted && !isCurrent && !isError && "bg-neutral-200 text-text-tertiary",
                  isError && "bg-error text-white"
                )}
              >
                {isCompleted ? (
                  <Check className="w-5 h-5" />
                ) : (
                  <span>{index + 1}</span>
                )}
              </div>
              
              {/* Step Label */}
              <div className="text-center">
                <div
                  className={cn(
                    "text-sm font-medium transition-colors duration-300",
                    isCurrent && "text-primary",
                    isCompleted && "text-success",
                    !isCurrent && !isCompleted && "text-text-secondary"
                  )}
                >
                  {step.title}
                </div>
                {showDescriptions && step.description && (
                  <div className="text-xs text-text-tertiary mt-1 max-w-24">
                    {step.description}
                  </div>
                )}
              </div>
            </div>
            
            {/* Connector */}
            {index < steps.length - 1 && (
              <div className="flex-1 mx-4">
                <div
                  className={cn(
                    "h-0.5 transition-colors duration-300",
                    isCompleted ? "bg-success" : "bg-neutral-200"
                  )}
                />
              </div>
            )}
          </React.Fragment>
        )
      })}
    </div>
  )
}

// Linear Progress Bar
interface LinearProgressProps {
  value: number
  max?: number
  className?: string
  showLabel?: boolean
  label?: string
  variant?: 'default' | 'success' | 'warning' | 'error'
  size?: 'sm' | 'md' | 'lg'
}

export function LinearProgress({ 
  value, 
  max = 100, 
  className,
  showLabel = false,
  label,
  variant = 'default',
  size = 'md'
}: LinearProgressProps) {
  const percentage = Math.min((value / max) * 100, 100)
  
  const variantClasses = {
    default: 'bg-primary',
    success: 'bg-success',
    warning: 'bg-warning',
    error: 'bg-error'
  }
  
  const sizeClasses = {
    sm: 'h-1',
    md: 'h-2',
    lg: 'h-3'
  }
  
  return (
    <div className={cn("w-full", className)}>
      {(showLabel || label) && (
        <div className="flex justify-between items-center mb-2">
          <span className="text-sm font-medium text-text-primary">
            {label || 'Progress'}
          </span>
          <span className="text-sm text-text-secondary">
            {Math.round(percentage)}%
          </span>
        </div>
      )}
      
      <div className={cn("w-full bg-neutral-200 rounded-full overflow-hidden", sizeClasses[size])}>
        <div
          className={cn(
            "h-full transition-all duration-500 ease-out rounded-full",
            variantClasses[variant]
          )}
          style={{ width: `${percentage}%` }}
        />
      </div>
    </div>
  )
}

// Circular Progress
interface CircularProgressProps {
  value: number
  max?: number
  size?: number
  strokeWidth?: number
  className?: string
  showLabel?: boolean
  label?: string
  variant?: 'default' | 'success' | 'warning' | 'error'
}

export function CircularProgress({ 
  value, 
  max = 100, 
  size = 120,
  strokeWidth = 8,
  className,
  showLabel = true,
  label,
  variant = 'default'
}: CircularProgressProps) {
  const percentage = Math.min((value / max) * 100, 100)
  const radius = (size - strokeWidth) / 2
  const circumference = radius * 2 * Math.PI
  const strokeDasharray = circumference
  const strokeDashoffset = circumference - (percentage / 100) * circumference
  
  const variantClasses = {
    default: 'stroke-primary',
    success: 'stroke-success',
    warning: 'stroke-warning',
    error: 'stroke-error'
  }
  
  return (
    <div className={cn("relative inline-flex items-center justify-center", className)}>
      <svg
        width={size}
        height={size}
        className="transform -rotate-90"
      >
        {/* Background circle */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke="currentColor"
          strokeWidth={strokeWidth}
          fill="none"
          className="text-neutral-200"
        />
        
        {/* Progress circle */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke="currentColor"
          strokeWidth={strokeWidth}
          fill="none"
          strokeDasharray={strokeDasharray}
          strokeDashoffset={strokeDashoffset}
          strokeLinecap="round"
          className={cn("transition-all duration-500 ease-out", variantClasses[variant])}
        />
      </svg>
      
      {/* Center content */}
      {showLabel && (
        <div className="absolute inset-0 flex flex-col items-center justify-center">
          <span className="text-2xl font-bold text-text-primary">
            {Math.round(percentage)}%
          </span>
          {label && (
            <span className="text-sm text-text-secondary mt-1">
              {label}
            </span>
          )}
        </div>
      )}
    </div>
  )
}

// Breadcrumb Progress (for navigation)
interface BreadcrumbProgressProps {
  steps: string[]
  currentStep: number
  onStepClick?: (step: number) => void
  className?: string
}

export function BreadcrumbProgress({ 
  steps, 
  currentStep, 
  onStepClick,
  className 
}: BreadcrumbProgressProps) {
  return (
    <nav className={cn("flex items-center space-x-1", className)} aria-label="Progress">
      {steps.map((step, index) => {
        const isCurrent = index === currentStep
        const isCompleted = index < currentStep
        const isClickable = onStepClick && (isCompleted || isCurrent)
        
        return (
          <React.Fragment key={index}>
            {isClickable ? (
              <button
                onClick={() => onStepClick(index)}
                className={cn(
                  "px-3 py-1 rounded-md text-sm font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2",
                  isCurrent && "bg-primary text-white",
                  isCompleted && "text-success hover:bg-success/10",
                  !isCurrent && !isCompleted && "text-text-tertiary"
                )}
                aria-current={isCurrent ? 'step' : undefined}
              >
                {step}
              </button>
            ) : (
              <span
                className={cn(
                  "px-3 py-1 text-sm font-medium",
                  isCurrent && "text-primary",
                  isCompleted && "text-success",
                  !isCurrent && !isCompleted && "text-text-tertiary"
                )}
                aria-current={isCurrent ? 'step' : undefined}
              >
                {step}
              </span>
            )}
            
            {index < steps.length - 1 && (
              <ChevronRight className="w-4 h-4 text-text-tertiary" />
            )}
          </React.Fragment>
        )
      })}
    </nav>
  )
}

// Loading Progress with Steps
interface LoadingProgressProps {
  steps: string[]
  currentStep: number
  className?: string
}

export function LoadingProgress({ steps, currentStep, className }: LoadingProgressProps) {
  return (
    <div className={cn("space-y-4", className)}>
      <LinearProgress 
        value={currentStep + 1} 
        max={steps.length} 
        showLabel 
        label="Loading Progress"
      />
      
      <div className="space-y-2">
        {steps.map((step, index) => {
          const isCompleted = index < currentStep
          const isCurrent = index === currentStep
          
          return (
            <div key={index} className="flex items-center gap-3">
              <div
                className={cn(
                  "w-4 h-4 rounded-full flex items-center justify-center transition-all duration-300",
                  isCompleted && "bg-success",
                  isCurrent && "bg-primary animate-pulse",
                  !isCompleted && !isCurrent && "bg-neutral-200"
                )}
              >
                {isCompleted && <Check className="w-2.5 h-2.5 text-white" />}
              </div>
              
              <span
                className={cn(
                  "text-sm transition-colors duration-300",
                  isCurrent && "text-primary font-medium",
                  isCompleted && "text-success",
                  !isCurrent && !isCompleted && "text-text-tertiary"
                )}
              >
                {step}
              </span>
            </div>
          )
        })}
      </div>
    </div>
  )
}
