import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"
import { AlertCircle, CheckCircle, Eye, EyeOff } from "lucide-react"

const enhancedInputVariants = cva(
  // Base styles using design system classes
  "input-base flex w-full rounded-md border border-border-default bg-surface text-text-primary transition-all duration-150 ease-in-out placeholder:text-text-tertiary focus:outline-none focus:border-primary focus:ring-3 focus:ring-primary/20 disabled:cursor-not-allowed disabled:bg-neutral-100 disabled:text-text-disabled disabled:opacity-50",
  {
    variants: {
      size: {
        sm: "input-sm h-8 px-3 text-xs",
        default: "input-md h-10 px-4 text-sm",
        lg: "input-lg h-12 px-6 text-base",
      },
      state: {
        default: "",
        error: "border-error focus:border-error focus:ring-error/20",
        success: "border-success focus:border-success focus:ring-success/20",
        warning: "border-warning focus:border-warning focus:ring-warning/20",
      },
    },
    defaultVariants: {
      size: "default",
      state: "default",
    },
  }
)

export interface EnhancedInputProps
  extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size'>,
    VariantProps<typeof enhancedInputVariants> {
  label?: string
  hint?: string
  error?: string
  success?: string
  warning?: string
  leftIcon?: React.ReactNode
  rightIcon?: React.ReactNode
  showPasswordToggle?: boolean
  containerClassName?: string
}

const EnhancedInput = React.forwardRef<HTMLInputElement, EnhancedInputProps>(
  ({
    className,
    type = "text",
    size,
    state,
    label,
    hint,
    error,
    success,
    warning,
    leftIcon,
    rightIcon,
    showPasswordToggle,
    containerClassName,
    id,
    ...props
  }, ref) => {
    const [showPassword, setShowPassword] = React.useState(false)
    const [internalType, setInternalType] = React.useState(type)
    
    // Determine the current state based on props
    const currentState = error ? 'error' : success ? 'success' : warning ? 'warning' : state || 'default'
    
    // Generate unique ID if not provided
    const inputId = id || React.useId()
    const hintId = `${inputId}-hint`
    const errorId = `${inputId}-error`
    
    // Handle password visibility toggle
    React.useEffect(() => {
      if (showPasswordToggle && type === 'password') {
        setInternalType(showPassword ? 'text' : 'password')
      }
    }, [showPassword, showPasswordToggle, type])
    
    const togglePasswordVisibility = () => {
      setShowPassword(!showPassword)
    }
    
    // Determine status icon
    const getStatusIcon = () => {
      switch (currentState) {
        case 'error':
          return <AlertCircle className="w-4 h-4 text-error" />
        case 'success':
          return <CheckCircle className="w-4 h-4 text-success" />
        case 'warning':
          return <AlertCircle className="w-4 h-4 text-warning" />
        default:
          return null
      }
    }
    
    const statusIcon = getStatusIcon()
    const hasRightContent = rightIcon || statusIcon || showPasswordToggle
    
    return (
      <div className={cn("space-y-2", containerClassName)}>
        {/* Label */}
        {label && (
          <label 
            htmlFor={inputId}
            className="text-label-large text-text-primary block"
          >
            {label}
          </label>
        )}
        
        {/* Input Container */}
        <div className="relative">
          {/* Left Icon */}
          {leftIcon && (
            <div className="absolute left-3 top-1/2 -translate-y-1/2 text-text-tertiary">
              {leftIcon}
            </div>
          )}
          
          {/* Input */}
          <input
            type={internalType}
            className={cn(
              enhancedInputVariants({ size, state: currentState }),
              leftIcon && "pl-10",
              hasRightContent && "pr-10",
              className
            )}
            ref={ref}
            id={inputId}
            aria-describedby={cn(
              hint && hintId,
              (error || success || warning) && errorId
            )}
            aria-invalid={currentState === 'error'}
            {...props}
          />
          
          {/* Right Content */}
          {hasRightContent && (
            <div className="absolute right-3 top-1/2 -translate-y-1/2 flex items-center gap-2">
              {/* Status Icon */}
              {statusIcon}
              
              {/* Custom Right Icon */}
              {rightIcon && !statusIcon && (
                <div className="text-text-tertiary">
                  {rightIcon}
                </div>
              )}
              
              {/* Password Toggle */}
              {showPasswordToggle && type === 'password' && (
                <button
                  type="button"
                  onClick={togglePasswordVisibility}
                  className="text-text-tertiary hover:text-text-primary transition-colors p-1 -m-1 rounded focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-1"
                  aria-label={showPassword ? "Hide password" : "Show password"}
                >
                  {showPassword ? (
                    <EyeOff className="w-4 h-4" />
                  ) : (
                    <Eye className="w-4 h-4" />
                  )}
                </button>
              )}
            </div>
          )}
        </div>
        
        {/* Hint Text */}
        {hint && !error && !success && !warning && (
          <p id={hintId} className="text-body-medium text-text-tertiary">
            {hint}
          </p>
        )}
        
        {/* Status Messages */}
        {(error || success || warning) && (
          <p 
            id={errorId} 
            className={cn(
              "text-body-medium flex items-center gap-2",
              error && "text-error",
              success && "text-success",
              warning && "text-warning"
            )}
          >
            {statusIcon}
            {error || success || warning}
          </p>
        )}
      </div>
    )
  }
)

EnhancedInput.displayName = "EnhancedInput"

export { EnhancedInput, enhancedInputVariants }

// Usage Examples:
/*
<EnhancedInput
  label="Email Address"
  placeholder="Enter your email"
  hint="We'll never share your email"
  leftIcon={<MailIcon />}
/>

<EnhancedInput
  label="Password"
  type="password"
  showPasswordToggle
  error="Password must be at least 8 characters"
/>

<EnhancedInput
  label="Amount"
  type="number"
  success="Valid amount entered"
  rightIcon={<DollarSignIcon />}
/>
*/
