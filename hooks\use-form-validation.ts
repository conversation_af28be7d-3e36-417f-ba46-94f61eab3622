import { useState, useCallback, useEffect } from 'react'

export interface ValidationRule {
  required?: boolean
  minLength?: number
  maxLength?: number
  pattern?: RegExp
  custom?: (value: string) => string | null
  match?: string // For password confirmation
}

export interface ValidationRules {
  [key: string]: ValidationRule
}

export interface FormErrors {
  [key: string]: string
}

export interface FormTouched {
  [key: string]: boolean
}

export interface UseFormValidationReturn {
  errors: FormErrors
  touched: FormTouched
  isValid: boolean
  validateField: (name: string, value: string, formData?: Record<string, string>) => string
  validateForm: (formData: Record<string, string>) => FormErrors
  setFieldTouched: (name: string, isTouched?: boolean) => void
  setFieldError: (name: string, error: string) => void
  clearFieldError: (name: string) => void
  clearAllErrors: () => void
  resetValidation: () => void
}

export function useFormValidation(rules: ValidationRules): UseFormValidationReturn {
  const [errors, setErrors] = useState<FormErrors>({})
  const [touched, setTouched] = useState<FormTouched>({})

  const validateField = useCallback((
    name: string, 
    value: string, 
    formData?: Record<string, string>
  ): string => {
    const rule = rules[name]
    if (!rule) return ''

    // Required validation
    if (rule.required && (!value || value.trim() === '')) {
      return `${name.charAt(0).toUpperCase() + name.slice(1)} is required`
    }

    // Skip other validations if field is empty and not required
    if (!value && !rule.required) return ''

    // Min length validation
    if (rule.minLength && value.length < rule.minLength) {
      return `${name.charAt(0).toUpperCase() + name.slice(1)} must be at least ${rule.minLength} characters`
    }

    // Max length validation
    if (rule.maxLength && value.length > rule.maxLength) {
      return `${name.charAt(0).toUpperCase() + name.slice(1)} must be no more than ${rule.maxLength} characters`
    }

    // Pattern validation
    if (rule.pattern && !rule.pattern.test(value)) {
      if (name === 'email') {
        return 'Please enter a valid email address'
      }
      return `${name.charAt(0).toUpperCase() + name.slice(1)} format is invalid`
    }

    // Match validation (for password confirmation)
    if (rule.match && formData && value !== formData[rule.match]) {
      return 'Passwords do not match'
    }

    // Custom validation
    if (rule.custom) {
      const customError = rule.custom(value)
      if (customError) return customError
    }

    return ''
  }, [rules])

  const validateForm = useCallback((formData: Record<string, string>): FormErrors => {
    const newErrors: FormErrors = {}
    
    Object.keys(rules).forEach(fieldName => {
      const error = validateField(fieldName, formData[fieldName] || '', formData)
      if (error) {
        newErrors[fieldName] = error
      }
    })

    return newErrors
  }, [rules, validateField])

  const setFieldTouched = useCallback((name: string, isTouched: boolean = true) => {
    setTouched(prev => ({ ...prev, [name]: isTouched }))
  }, [])

  const setFieldError = useCallback((name: string, error: string) => {
    setErrors(prev => ({ ...prev, [name]: error }))
  }, [])

  const clearFieldError = useCallback((name: string) => {
    setErrors(prev => {
      const newErrors = { ...prev }
      delete newErrors[name]
      return newErrors
    })
  }, [])

  const clearAllErrors = useCallback(() => {
    setErrors({})
  }, [])

  const resetValidation = useCallback(() => {
    setErrors({})
    setTouched({})
  }, [])

  const isValid = Object.keys(errors).length === 0

  return {
    errors,
    touched,
    isValid,
    validateField,
    validateForm,
    setFieldTouched,
    setFieldError,
    clearFieldError,
    clearAllErrors,
    resetValidation
  }
}

// Predefined validation rules
export const commonValidationRules = {
  email: {
    required: true,
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  },
  password: {
    required: true,
    minLength: 8,
    custom: (value: string) => {
      const hasLower = /[a-z]/.test(value)
      const hasUpper = /[A-Z]/.test(value)
      const hasNumber = /\d/.test(value)
      const hasSpecial = /[^a-zA-Z\d]/.test(value)
      
      if (!hasLower || !hasUpper || !hasNumber || !hasSpecial) {
        return 'Password must contain at least one lowercase letter, one uppercase letter, one number, and one special character'
      }
      return null
    }
  },
  confirmPassword: {
    required: true,
    match: 'password'
  },
  fullName: {
    required: true,
    minLength: 2,
    maxLength: 100
  },
  required: {
    required: true
  }
}
