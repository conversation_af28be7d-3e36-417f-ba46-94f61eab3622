{"name": "my-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "analyze": "node scripts/analyze-bundle.js", "analyze:json": "node scripts/analyze-bundle.js --json", "find-unused": "node scripts/find-unused-imports.js", "test:errors": "echo 'Visit http://localhost:3000/debug/error-testing to test error boundaries'", "test:a11y": "echo 'Visit http://localhost:3000/debug/accessibility-testing to test accessibility'", "test:perf": "echo 'Visit http://localhost:3000/debug/performance-testing to test performance'", "test:env": "echo 'Visit http://localhost:3000/debug/env to check environment variables'"}, "dependencies": {"@ai-sdk/google": "^1.2.22", "@ai-sdk/react": "^1.2.12", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.52.1", "@tabler/icons-react": "^3.34.1", "@tanstack/react-table": "^8.21.3", "ai": "^4.3.19", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lightningcss": "^1.30.1", "lucide-react": "^0.526.0", "next": "15.4.4", "next-themes": "^0.4.6", "react": "19.1.0", "react-dom": "19.1.0", "react-dropzone": "^14.3.8", "react-markdown": "^10.1.0", "recharts": "^2.15.4", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "vaul": "^1.1.2", "zod": "^3.25.76"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.4", "tailwindcss": "^4", "tw-animate-css": "^1.3.6", "typescript": "^5"}}