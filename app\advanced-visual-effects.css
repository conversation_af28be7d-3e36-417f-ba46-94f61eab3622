/* ===================================================================
   ADVANCED VISUAL EFFECTS - PREMIUM PROFESSIONAL STANDARDS
   ================================================================== */

/* ===================================================================
   1. SOPHISTICATED SHADOW SYSTEM
   ================================================================== */

:root {
  /* Elevation System - 8 Levels */
  --shadow-elevation-0: none;
  --shadow-elevation-1: 
    0 1px 2px 0 rgba(0, 0, 0, 0.05),
    0 1px 3px 0 rgba(0, 0, 0, 0.1);
  --shadow-elevation-2: 
    0 1px 3px 0 rgba(0, 0, 0, 0.1),
    0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-elevation-3: 
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-elevation-4: 
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-elevation-5: 
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-elevation-6: 
    0 25px 50px -12px rgba(0, 0, 0, 0.25),
    0 12px 24px -8px rgba(0, 0, 0, 0.08);
  --shadow-elevation-7: 
    0 32px 64px -12px rgba(0, 0, 0, 0.4),
    0 16px 32px -8px rgba(0, 0, 0, 0.12);
  --shadow-elevation-8: 
    0 40px 80px -16px rgba(0, 0, 0, 0.5),
    0 20px 40px -12px rgba(0, 0, 0, 0.15);

  /* Colored Shadows */
  --shadow-primary: 0 8px 32px -8px rgba(34, 197, 94, 0.3);
  --shadow-success: 0 8px 32px -8px rgba(34, 197, 94, 0.25);
  --shadow-warning: 0 8px 32px -8px rgba(251, 191, 36, 0.25);
  --shadow-error: 0 8px 32px -8px rgba(239, 68, 68, 0.25);
  
  /* Inner Shadows */
  --shadow-inset-sm: inset 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-inset-md: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
  --shadow-inset-lg: inset 0 4px 8px 0 rgba(0, 0, 0, 0.1);
}

/* Shadow Utility Classes */
.elevation-0 { box-shadow: var(--shadow-elevation-0); }
.elevation-1 { box-shadow: var(--shadow-elevation-1); }
.elevation-2 { box-shadow: var(--shadow-elevation-2); }
.elevation-3 { box-shadow: var(--shadow-elevation-3); }
.elevation-4 { box-shadow: var(--shadow-elevation-4); }
.elevation-5 { box-shadow: var(--shadow-elevation-5); }
.elevation-6 { box-shadow: var(--shadow-elevation-6); }
.elevation-7 { box-shadow: var(--shadow-elevation-7); }
.elevation-8 { box-shadow: var(--shadow-elevation-8); }

.shadow-primary { box-shadow: var(--shadow-primary); }
.shadow-success { box-shadow: var(--shadow-success); }
.shadow-warning { box-shadow: var(--shadow-warning); }
.shadow-error { box-shadow: var(--shadow-error); }

/* ===================================================================
   2. GLASSMORPHISM EFFECTS
   ================================================================== */

.glass-morphism {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 
    0 8px 32px 0 rgba(31, 38, 135, 0.37),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.glass-morphism-dark {
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 
    0 8px 32px 0 rgba(0, 0, 0, 0.37),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.glass-navigation {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(12px) saturate(180%);
  -webkit-backdrop-filter: blur(12px) saturate(180%);
  border-bottom: 1px solid rgba(255, 255, 255, 0.3);
}

.glass-modal {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(24px) saturate(200%);
  -webkit-backdrop-filter: blur(24px) saturate(200%);
  border: 1px solid rgba(255, 255, 255, 0.4);
  box-shadow: var(--shadow-elevation-6);
}

/* ===================================================================
   3. GRADIENT OVERLAYS
   ================================================================== */

.gradient-overlay-primary {
  position: relative;
}

.gradient-overlay-primary::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(
    135deg,
    rgba(34, 197, 94, 0.1) 0%,
    rgba(34, 197, 94, 0.05) 50%,
    transparent 100%
  );
  pointer-events: none;
  border-radius: inherit;
}

.gradient-overlay-success {
  position: relative;
}

.gradient-overlay-success::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(
    135deg,
    rgba(34, 197, 94, 0.15) 0%,
    rgba(34, 197, 94, 0.08) 50%,
    transparent 100%
  );
  pointer-events: none;
  border-radius: inherit;
}

.gradient-mesh-bg {
  background: 
    radial-gradient(circle at 20% 80%, rgba(34, 197, 94, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(168, 85, 247, 0.05) 0%, transparent 50%);
}

/* ===================================================================
   4. TEXTURE AND NOISE EFFECTS
   ================================================================== */

.texture-noise {
  position: relative;
}

.texture-noise::before {
  content: '';
  position: absolute;
  inset: 0;
  background-image: 
    radial-gradient(circle at 1px 1px, rgba(0, 0, 0, 0.15) 1px, transparent 0);
  background-size: 20px 20px;
  opacity: 0.03;
  pointer-events: none;
  border-radius: inherit;
}

.texture-grain {
  position: relative;
}

.texture-grain::before {
  content: '';
  position: absolute;
  inset: 0;
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.9' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E");
  opacity: 0.02;
  pointer-events: none;
  border-radius: inherit;
}

.texture-paper {
  position: relative;
  background-color: #fefefe;
}

.texture-paper::before {
  content: '';
  position: absolute;
  inset: 0;
  background-image: 
    repeating-linear-gradient(
      45deg,
      transparent,
      transparent 2px,
      rgba(0, 0, 0, 0.01) 2px,
      rgba(0, 0, 0, 0.01) 4px
    );
  pointer-events: none;
  border-radius: inherit;
}

/* ===================================================================
   5. BACKDROP BLUR EFFECTS
   ================================================================== */

.backdrop-blur-sm {
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

.backdrop-blur-md {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.backdrop-blur-lg {
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
}

.backdrop-blur-xl {
  backdrop-filter: blur(24px);
  -webkit-backdrop-filter: blur(24px);
}

.backdrop-blur-2xl {
  backdrop-filter: blur(40px);
  -webkit-backdrop-filter: blur(40px);
}

/* ===================================================================
   6. PARALLAX SCROLLING
   ================================================================== */

.parallax-container {
  height: 100vh;
  overflow-x: hidden;
  overflow-y: auto;
  perspective: 1px;
}

.parallax-element {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.parallax-back {
  transform: translateZ(-1px) scale(2);
}

.parallax-base {
  transform: translateZ(0);
}

.parallax-front {
  transform: translateZ(0.5px) scale(0.5);
}

/* ===================================================================
   7. PREMIUM CARD STYLING
   ================================================================== */

.card-premium-elevated {
  background: linear-gradient(
    145deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(255, 255, 255, 0.8) 100%
  );
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 
    var(--shadow-elevation-3),
    inset 0 1px 0 rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-premium-elevated:hover {
  transform: translateY(-2px);
  box-shadow: 
    var(--shadow-elevation-5),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

.card-premium-floating {
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.4);
  box-shadow: 
    0 20px 40px -12px rgba(0, 0, 0, 0.15),
    0 8px 16px -4px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(16px) saturate(180%);
  -webkit-backdrop-filter: blur(16px) saturate(180%);
}

.card-premium-glass {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 100%
  );
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 
    0 8px 32px 0 rgba(31, 38, 135, 0.37),
    inset 0 1px 0 rgba(255, 255, 255, 0.3),
    inset 0 -1px 0 rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px) saturate(200%);
  -webkit-backdrop-filter: blur(20px) saturate(200%);
}

/* ===================================================================
   8. ELEGANT DIVIDERS AND SEPARATORS
   ================================================================== */

.divider-gradient {
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(0, 0, 0, 0.1) 20%,
    rgba(0, 0, 0, 0.2) 50%,
    rgba(0, 0, 0, 0.1) 80%,
    transparent 100%
  );
}

.divider-gradient-vertical {
  width: 1px;
  background: linear-gradient(
    180deg,
    transparent 0%,
    rgba(0, 0, 0, 0.1) 20%,
    rgba(0, 0, 0, 0.2) 50%,
    rgba(0, 0, 0, 0.1) 80%,
    transparent 100%
  );
}

.divider-glow {
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(34, 197, 94, 0.3) 20%,
    rgba(34, 197, 94, 0.6) 50%,
    rgba(34, 197, 94, 0.3) 80%,
    transparent 100%
  );
  box-shadow: 0 0 8px rgba(34, 197, 94, 0.3);
}

.separator-elegant {
  position: relative;
  text-align: center;
  margin: 2rem 0;
}

.separator-elegant::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(0, 0, 0, 0.1) 25%,
    rgba(0, 0, 0, 0.2) 50%,
    rgba(0, 0, 0, 0.1) 75%,
    transparent 100%
  );
}

.separator-elegant span {
  background: var(--background);
  padding: 0 1rem;
  color: var(--text-tertiary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

/* ===================================================================
   9. DARK MODE ADAPTATIONS
   ================================================================== */

.dark .glass-morphism {
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 
    0 8px 32px 0 rgba(0, 0, 0, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.dark .glass-navigation {
  background: rgba(0, 0, 0, 0.8);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.dark .card-premium-elevated {
  background: linear-gradient(
    145deg,
    rgba(0, 0, 0, 0.4) 0%,
    rgba(0, 0, 0, 0.2) 100%
  );
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 
    var(--shadow-elevation-3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.dark .divider-gradient {
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.1) 20%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0.1) 80%,
    transparent 100%
  );
}

/* ===================================================================
   10. ANIMATION ENHANCEMENTS
   ================================================================== */

.float-animation {
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.glow-pulse {
  animation: glow-pulse 2s ease-in-out infinite alternate;
}

@keyframes glow-pulse {
  from { box-shadow: 0 0 20px rgba(34, 197, 94, 0.3); }
  to { box-shadow: 0 0 30px rgba(34, 197, 94, 0.6); }
}

.shimmer-effect {
  position: relative;
  overflow: hidden;
}

.shimmer-effect::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.4) 50%,
    transparent 100%
  );
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}
