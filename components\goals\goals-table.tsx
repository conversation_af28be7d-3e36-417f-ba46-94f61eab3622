'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/lib/contexts/auth-context'
import { getUserGoals, deleteGoal, updateGoal } from '@/lib/supabase/queries'
import { Goal } from '@/lib/types/database'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { GoalStatusIndicator, ProgressIndicator } from '@/components/ui/status-indicator'
import { ErrorBoundary } from '@/components/error-boundary'
import { LoadingError } from '@/components/error/error-messages'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu'
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { 
  Search, 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  Plus,
  Target,
  Calendar,
  DollarSign,
  TrendingUp
} from 'lucide-react'
import { format, differenceInDays, differenceInMonths } from 'date-fns'

interface GoalsTableProps {
  onEditGoal: (goal: Goal) => void
  onAddGoal: () => void
  refreshTrigger?: number
}

export function GoalsTable({ onEditGoal, onAddGoal, refreshTrigger }: GoalsTableProps) {
  const { user, profile } = useAuth()
  const [goals, setGoals] = useState<Goal[]>([])
  const [filteredGoals, setFilteredGoals] = useState<Goal[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [goalToDelete, setGoalToDelete] = useState<Goal | null>(null)
  const [contributionDialogOpen, setContributionDialogOpen] = useState(false)
  const [selectedGoal, setSelectedGoal] = useState<Goal | null>(null)
  const [contributionAmount, setContributionAmount] = useState('')
  
  // Filter states
  const [searchTerm, setSearchTerm] = useState('')

  useEffect(() => {
    loadGoals()
  }, [user, refreshTrigger])

  useEffect(() => {
    applyFilters()
  }, [goals, searchTerm])

  const loadGoals = async () => {
    if (!user) return
    
    setLoading(true)
    try {
      const userGoals = await getUserGoals(user.id)
      setGoals(userGoals)
    } catch (err) {
      console.error('Error loading goals:', err)
      setError('Failed to load goals')
    } finally {
      setLoading(false)
    }
  }

  const applyFilters = () => {
    let filtered = [...goals]

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(goal =>
        goal.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        goal.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (goal.description && goal.description.toLowerCase().includes(searchTerm.toLowerCase()))
      )
    }

    // Sort by progress (closest to completion first)
    filtered.sort((a, b) => {
      const progressA = (a.current_amount / a.target_amount) * 100
      const progressB = (b.current_amount / b.target_amount) * 100
      return progressB - progressA
    })

    setFilteredGoals(filtered)
  }

  const handleDeleteGoal = async (goal: Goal) => {
    try {
      await deleteGoal(goal.id)
      await loadGoals()
      setDeleteDialogOpen(false)
      setGoalToDelete(null)
    } catch (err) {
      console.error('Error deleting goal:', err)
      setError('Failed to delete goal')
    }
  }

  const handleAddContribution = async () => {
    if (!selectedGoal || !contributionAmount) return
    
    try {
      const newAmount = selectedGoal.current_amount + Number(contributionAmount)
      await updateGoal(selectedGoal.id, { current_amount: newAmount })
      await loadGoals()
      setContributionDialogOpen(false)
      setSelectedGoal(null)
      setContributionAmount('')
    } catch (err) {
      console.error('Error adding contribution:', err)
      setError('Failed to add contribution')
    }
  }

  const formatAmount = (amount: number) => {
    const currency = profile?.currency_code || 'USD'
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount)
  }

  const calculateProgress = (goal: Goal) => {
    return Math.min((goal.current_amount / goal.target_amount) * 100, 100)
  }

  const getDaysRemaining = (targetDate: string) => {
    return differenceInDays(new Date(targetDate), new Date())
  }

  const getMonthsRemaining = (targetDate: string) => {
    return differenceInMonths(new Date(targetDate), new Date())
  }

  const getMonthlySavingsNeeded = (goal: Goal) => {
    const remaining = goal.target_amount - goal.current_amount
    const months = getMonthsRemaining(goal.target_date)
    return months > 0 ? remaining / months : 0
  }

  const getGoalStatus = (goal: Goal) => {
    const progress = calculateProgress(goal)
    const daysRemaining = getDaysRemaining(goal.target_date)
    
    if (progress >= 100) return 'completed'
    if (daysRemaining < 0) return 'overdue'
    if (daysRemaining <= 30) return 'urgent'
    return 'active'
  }

  const getStatusBadge = (goal: Goal) => {
    const status = getGoalStatus(goal)
    const progress = calculateProgress(goal)
    const daysRemaining = getDaysRemaining(goal.target_date)

    // Map internal status to accessible status indicator
    const statusMap = {
      'completed': 'completed' as const,
      'overdue': 'error' as const,
      'urgent': 'error' as const,
      'active': 'active' as const
    }

    const accessibleStatus = statusMap[status as keyof typeof statusMap] || 'pending'

    // Create detailed aria label with context
    const ariaLabel = `Goal status: ${status}. Progress: ${Math.round(progress)}%. ${
      daysRemaining >= 0 ? `${daysRemaining} days remaining` : `${Math.abs(daysRemaining)} days overdue`
    }`

    return (
      <GoalStatusIndicator
        status={accessibleStatus}
        progress={progress}
        size="sm"
        aria-label={ariaLabel}
      />
    )
  }

  const getCategoryIcon = (category: string) => {
    const icons: { [key: string]: string } = {
      emergency_fund: '🛡️',
      vacation: '✈️',
      house_down_payment: '🏠',
      car: '🚗',
      education: '🎓',
      retirement: '🏖️',
      debt_payoff: '💳',
      investment: '📈',
      wedding: '💒',
      other: '🎯'
    }
    return icons[category] || '🎯'
  }

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading goals...</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <ErrorBoundary>
      <div className="space-y-4">
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Target className="w-5 h-5" />
                Financial Goals
              </CardTitle>
              <CardDescription>
                Track your progress towards your financial objectives
              </CardDescription>
            </div>
            <Button onClick={onAddGoal}>
              <Plus className="w-4 h-4 mr-2" />
              Add Goal
            </Button>
          </div>
        </CardHeader>

        <CardContent>
          {/* Search */}
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <Input
                  placeholder="Search goals..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
          </div>

          {/* Goals Grid */}
          {filteredGoals.length === 0 ? (
            <div className="text-center py-12">
              <Target className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-2">
                No goals found
              </h3>
              <p className="text-muted-foreground mb-4">
                Create your first financial goal to start tracking your progress
              </p>
              <Button onClick={onAddGoal}>
                <Plus className="w-4 h-4 mr-2" />
                Create Your First Goal
              </Button>
            </div>
          ) : (
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {filteredGoals.map((goal) => (
                <Card key={goal.id} className="relative">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-2">
                        <span className="text-2xl">{getCategoryIcon(goal.category)}</span>
                        <div>
                          <CardTitle className="text-lg">{goal.name}</CardTitle>
                          <CardDescription className="capitalize">
                            {goal.category?.replace('_', ' ') || 'Uncategorized'}
                          </CardDescription>
                        </div>
                      </div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="w-4 h-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => onEditGoal(goal)}>
                            <Edit className="w-4 h-4 mr-2" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem 
                            onClick={() => {
                              setSelectedGoal(goal)
                              setContributionDialogOpen(true)
                            }}
                          >
                            <DollarSign className="w-4 h-4 mr-2" />
                            Add Contribution
                          </DropdownMenuItem>
                          <DropdownMenuItem 
                            onClick={() => {
                              setGoalToDelete(goal)
                              setDeleteDialogOpen(true)
                            }}
                            className="text-red-600"
                          >
                            <Trash2 className="w-4 h-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </CardHeader>

                  <CardContent className="space-y-4">
                    {/* Status Badge */}
                    <div className="flex justify-between items-center">
                      {getStatusBadge(goal)}
                      <span className="text-sm text-muted-foreground">
                        {getDaysRemaining(goal.target_date)} days left
                      </span>
                    </div>

                    {/* Progress */}
                    <div className="space-y-2">
                      <ProgressIndicator
                        value={goal.current_amount}
                        max={goal.target_amount}
                        label={`${goal.name} progress`}
                        className="w-full"
                      />
                      <div className="flex justify-between text-sm text-muted-foreground">
                        <span>{formatAmount(goal.current_amount)}</span>
                        <span>{formatAmount(goal.target_amount)}</span>
                      </div>
                    </div>

                    {/* Target Date */}
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Calendar className="w-4 h-4" />
                      Target: {format(new Date(goal.target_date), 'MMM dd, yyyy')}
                    </div>

                    {/* Monthly Savings Needed */}
                    <div className="flex items-center gap-2 text-sm">
                      <TrendingUp className="w-4 h-4 text-primary" />
                      <span>
                        <span className="text-muted-foreground">Monthly needed: </span>
                        <span className="font-medium">
                          {formatAmount(getMonthlySavingsNeeded(goal))}
                        </span>
                      </span>
                    </div>

                    {/* Description */}
                    {goal.description && (
                      <p className="text-sm text-muted-foreground">
                        {goal.description}
                      </p>
                    )}

                    {/* Quick Add Contribution Button */}
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="w-full"
                      onClick={() => {
                        setSelectedGoal(goal)
                        setContributionDialogOpen(true)
                      }}
                    >
                      <DollarSign className="w-4 h-4 mr-2" />
                      Add Contribution
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Goal</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete "{goalToDelete?.name}"? 
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => goalToDelete && handleDeleteGoal(goalToDelete)}
              className="bg-red-600 hover:bg-red-700"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Add Contribution Dialog */}
      <Dialog open={contributionDialogOpen} onOpenChange={setContributionDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add Contribution</DialogTitle>
            <DialogDescription>
              Add money to your "{selectedGoal?.name}" goal
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium">
                Contribution Amount ({profile?.currency_code || 'USD'})
              </label>
              <Input
                type="number"
                step="0.01"
                min="0"
                placeholder="0.00"
                value={contributionAmount}
                onChange={(e) => setContributionAmount(e.target.value)}
                className="mt-1"
              />
            </div>
            
            {selectedGoal && contributionAmount && (
              <div className="p-3 bg-muted rounded-lg">
                <div className="text-sm text-muted-foreground">New total will be:</div>
                <div className="font-medium">
                  {formatAmount(selectedGoal.current_amount + Number(contributionAmount))} 
                  <span className="text-muted-foreground">
                    {' '}of {formatAmount(selectedGoal.target_amount)}
                  </span>
                </div>
              </div>
            )}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setContributionDialogOpen(false)}>
              Cancel
            </Button>
            <Button 
              onClick={handleAddContribution}
              disabled={!contributionAmount || Number(contributionAmount) <= 0}
            >
              Add Contribution
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      </div>
    </ErrorBoundary>
  )
}
