'use client'

import { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import Link from 'next/link'
import { useAuth } from '@/lib/contexts/auth-context'
import { AuthGuard } from '@/lib/middleware/auth-guard'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2 } from 'lucide-react'
import {
  AccessibleInput,
  AccessiblePasswordInput,
  FormErrorSummary
} from '@/components/ui/accessible-form'
import { useFormValidation } from '@/hooks/use-form-validation'
import { ResponsiveContainer, MobileOptimizedCard } from '@/components/ui/responsive-container'
import { SimpleThemeToggle } from '@/components/theme/theme-provider'

export default function SignInPage() {
  const [formData, setFormData] = useState({
    email: '',
    password: '',
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [message, setMessage] = useState('')

  const { signIn } = useAuth()
  const router = useRouter()
  const searchParams = useSearchParams()

  // Form validation setup
  const validationRules = {
    email: { required: true, pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/ },
    password: { required: true, minLength: 1 }
  }

  const {
    errors,
    touched,
    validateField,
    validateForm,
    setFieldTouched,
    clearFieldError,
    resetValidation
  } = useFormValidation(validationRules)

  useEffect(() => {
    // Check for messages from URL params (e.g., from signup redirect)
    const urlMessage = searchParams.get('message')
    if (urlMessage) {
      setMessage(urlMessage)
    }
  }, [searchParams])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
    setError('') // Clear general error when user starts typing

    // Real-time validation for touched fields
    if (touched[name]) {
      const fieldError = validateField(name, value, formData)
      if (!fieldError) {
        clearFieldError(name)
      }
    }
  }

  const handleFieldBlur = (name: string, value: string) => {
    setFieldTouched(name, true)
    const fieldError = validateField(name, value, formData)
    // The validation hook will handle setting the error
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Validate entire form
    const formErrors = validateForm(formData)

    // Mark all fields as touched to show errors
    Object.keys(validationRules).forEach(field => {
      setFieldTouched(field, true)
    })

    if (Object.keys(formErrors).length > 0) {
      // Focus on first error field
      const firstErrorField = Object.keys(formErrors)[0]
      const element = document.getElementById(firstErrorField)
      if (element) {
        element.focus()
        element.scrollIntoView({ behavior: 'smooth', block: 'center' })
      }
      return
    }

    setLoading(true)
    setError('')
    setMessage('')

    try {
      const { error } = await signIn(formData.email, formData.password)

      if (error) {
        if (error.message.includes('Invalid login credentials')) {
          setError('Invalid email or password. Please try again.')
        } else if (error.message.includes('Email not confirmed')) {
          setError('Please check your email and confirm your account before signing in.')
        } else {
          setError(error.message)
        }
      } else {
        resetValidation()
        // Successful sign in - redirect will be handled by middleware
        router.push('/onboarding')
      }
    } catch (err) {
      setError('An unexpected error occurred. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  return (
    <AuthGuard requireAuth={false}>
      <div className="min-h-screen flex items-center justify-center bg-background py-4 sm:py-12">
        <ResponsiveContainer maxWidth="md" padding="md">
          <MobileOptimizedCard variant="elevated">
          <CardHeader className="space-y-1">
            <div className="flex justify-between items-start">
              <div className="flex-1">
                <CardTitle className="text-2xl font-bold text-center">Welcome Back</CardTitle>
                <CardDescription className="text-center">
                  Sign in to your Personal Finance Tracker account
                </CardDescription>
              </div>
              <SimpleThemeToggle />
            </div>
          </CardHeader>

          <form onSubmit={handleSubmit} noValidate>
            <CardContent className="space-y-4">
              {/* Form Error Summary */}
              <FormErrorSummary errors={errors} />

              {error && (
                <Alert variant="destructive" role="alert">
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              {message && (
                <Alert role="status">
                  <AlertDescription>{message}</AlertDescription>
                </Alert>
              )}

              <AccessibleInput
                id="email"
                label="Email Address"
                type="email"
                value={formData.email}
                onChange={handleInputChange}
                onBlur={(e) => handleFieldBlur('email', e.target.value)}
                placeholder="Enter your email address"
                required
                disabled={loading}
                error={touched.email ? errors.email : ''}
                autoComplete="email"
              />

              <AccessiblePasswordInput
                id="password"
                label="Password"
                value={formData.password}
                onChange={handleInputChange}
                onBlur={(e) => handleFieldBlur('password', e.target.value)}
                placeholder="Enter your password"
                required
                disabled={loading}
                error={touched.password ? errors.password : ''}
                autoComplete="current-password"
              />
            </CardContent>

            <CardFooter className="flex flex-col space-y-4">
              <Button
                type="submit"
                className="w-full"
                disabled={loading}
                aria-describedby={Object.keys(errors).length > 0 ? "form-errors" : undefined}
              >
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" aria-hidden="true" />
                    Signing In...
                  </>
                ) : (
                  'Sign In'
                )}
              </Button>

              <div className="text-sm text-center space-y-2">
                <p className="text-muted-foreground">
                  Don&apos;t have an account?{' '}
                  <Link
                    href="/auth/signup"
                    className="text-primary hover:underline focus:ring-2 focus:ring-ring focus:ring-offset-2 rounded"
                  >
                    Sign up
                  </Link>
                </p>
                <p>
                  <Link
                    href="/auth/forgot-password"
                    className="text-primary hover:underline focus:ring-2 focus:ring-ring focus:ring-offset-2 rounded"
                  >
                    Forgot your password?
                  </Link>
                </p>
              </div>
            </CardFooter>
          </form>
          </MobileOptimizedCard>
        </ResponsiveContainer>
      </div>
    </AuthGuard>
  )
}
