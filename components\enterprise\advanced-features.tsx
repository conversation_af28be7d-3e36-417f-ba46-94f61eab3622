'use client'

import React, { useState, useEffect, useRef } from 'react'
import { cn } from '@/lib/utils'
import { 
  Bell, 
  Settings, 
  User, 
  Shield, 
  Activity, 
  Download, 
  Upload,
  Search,
  Filter,
  Calendar,
  Clock,
  CheckCircle,
  AlertTriangle,
  Info,
  X,
  Maximize2,
  Minimize2,
  Copy,
  ExternalLink,
  Keyboard,
  Zap,
  Sync,
  Users,
  MessageSquare,
  Eye,
  EyeOff
} from 'lucide-react'

// Smart Notification System
interface Notification {
  id: string
  title: string
  message: string
  type: 'info' | 'success' | 'warning' | 'error'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  timestamp: Date
  read: boolean
  actionable?: boolean
  action?: {
    label: string
    onClick: () => void
  }
}

interface SmartNotificationSystemProps {
  notifications: Notification[]
  onMarkAsRead: (id: string) => void
  onMarkAllAsRead: () => void
  onDismiss: (id: string) => void
  className?: string
}

export function SmartNotificationSystem({
  notifications,
  onMarkAsRead,
  onMarkAllAsRead,
  onDismiss,
  className
}: SmartNotificationSystemProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [filter, setFilter] = useState<'all' | 'unread' | 'urgent'>('all')
  
  const unreadCount = notifications.filter(n => !n.read).length
  const urgentCount = notifications.filter(n => n.priority === 'urgent' && !n.read).length
  
  const filteredNotifications = notifications.filter(notification => {
    switch (filter) {
      case 'unread':
        return !notification.read
      case 'urgent':
        return notification.priority === 'urgent'
      default:
        return true
    }
  }).sort((a, b) => {
    // Sort by priority first, then by timestamp
    const priorityOrder = { urgent: 4, high: 3, medium: 2, low: 1 }
    const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority]
    if (priorityDiff !== 0) return priorityDiff
    return b.timestamp.getTime() - a.timestamp.getTime()
  })
  
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'success': return <CheckCircle className="w-5 h-5 text-success" />
      case 'warning': return <AlertTriangle className="w-5 h-5 text-warning" />
      case 'error': return <AlertTriangle className="w-5 h-5 text-error" />
      default: return <Info className="w-5 h-5 text-info" />
    }
  }
  
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'border-l-error'
      case 'high': return 'border-l-warning'
      case 'medium': return 'border-l-info'
      default: return 'border-l-neutral-300'
    }
  }
  
  return (
    <div className={cn("relative", className)}>
      {/* Notification Bell */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="relative p-2 text-text-secondary hover:text-text-primary transition-colors rounded-lg hover:bg-neutral-100 dark:hover:bg-neutral-800"
      >
        <Bell className="w-6 h-6" />
        
        {/* Badge */}
        {unreadCount > 0 && (
          <span className={cn(
            "absolute -top-1 -right-1 min-w-5 h-5 flex items-center justify-center text-xs font-medium text-white rounded-full",
            urgentCount > 0 ? "bg-error animate-pulse" : "bg-primary"
          )}>
            {unreadCount > 99 ? '99+' : unreadCount}
          </span>
        )}
      </button>
      
      {/* Notification Panel */}
      {isOpen && (
        <>
          {/* Backdrop */}
          <div 
            className="fixed inset-0 z-40" 
            onClick={() => setIsOpen(false)}
          />
          
          {/* Panel */}
          <div className="absolute top-full right-0 mt-2 w-96 max-h-96 bg-surface border border-border-default rounded-lg shadow-lg z-50 overflow-hidden">
            {/* Header */}
            <div className="p-4 border-b border-border-subtle">
              <div className="flex items-center justify-between mb-3">
                <h3 className="font-semibold text-text-primary">Notifications</h3>
                <button
                  onClick={() => setIsOpen(false)}
                  className="text-text-tertiary hover:text-text-primary transition-colors"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
              
              {/* Filters */}
              <div className="flex gap-2">
                {['all', 'unread', 'urgent'].map((filterOption) => (
                  <button
                    key={filterOption}
                    onClick={() => setFilter(filterOption as any)}
                    className={cn(
                      "px-3 py-1 text-xs font-medium rounded-full transition-colors capitalize",
                      filter === filterOption
                        ? "bg-primary text-white"
                        : "bg-neutral-100 text-text-secondary hover:bg-neutral-200"
                    )}
                  >
                    {filterOption}
                  </button>
                ))}
              </div>
              
              {/* Mark All Read */}
              {unreadCount > 0 && (
                <button
                  onClick={onMarkAllAsRead}
                  className="mt-2 text-xs text-primary hover:text-primary-600 transition-colors"
                >
                  Mark all as read
                </button>
              )}
            </div>
            
            {/* Notifications List */}
            <div className="max-h-80 overflow-y-auto">
              {filteredNotifications.length === 0 ? (
                <div className="p-8 text-center text-text-tertiary">
                  <Bell className="w-8 h-8 mx-auto mb-2 opacity-50" />
                  <p className="text-sm">No notifications</p>
                </div>
              ) : (
                filteredNotifications.map((notification) => (
                  <div
                    key={notification.id}
                    className={cn(
                      "p-4 border-l-4 hover:bg-neutral-50 dark:hover:bg-neutral-800 transition-colors",
                      getPriorityColor(notification.priority),
                      !notification.read && "bg-primary/5"
                    )}
                  >
                    <div className="flex items-start gap-3">
                      {getNotificationIcon(notification.type)}
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between gap-2">
                          <h4 className={cn(
                            "text-sm font-medium",
                            !notification.read ? "text-text-primary" : "text-text-secondary"
                          )}>
                            {notification.title}
                          </h4>
                          
                          <button
                            onClick={() => onDismiss(notification.id)}
                            className="text-text-tertiary hover:text-text-primary transition-colors flex-shrink-0"
                          >
                            <X className="w-3 h-3" />
                          </button>
                        </div>
                        
                        <p className="text-xs text-text-tertiary mt-1 leading-relaxed">
                          {notification.message}
                        </p>
                        
                        <div className="flex items-center justify-between mt-2">
                          <span className="text-xs text-text-tertiary">
                            {notification.timestamp.toLocaleTimeString()}
                          </span>
                          
                          <div className="flex items-center gap-2">
                            {notification.actionable && notification.action && (
                              <button
                                onClick={notification.action.onClick}
                                className="text-xs text-primary hover:text-primary-600 font-medium transition-colors"
                              >
                                {notification.action.label}
                              </button>
                            )}
                            
                            {!notification.read && (
                              <button
                                onClick={() => onMarkAsRead(notification.id)}
                                className="text-xs text-text-tertiary hover:text-text-primary transition-colors"
                              >
                                Mark as read
                              </button>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        </>
      )}
    </div>
  )
}

// Advanced Keyboard Shortcuts Overlay
interface KeyboardShortcut {
  keys: string[]
  description: string
  category: string
}

interface KeyboardShortcutsOverlayProps {
  isVisible: boolean
  onClose: () => void
  shortcuts: KeyboardShortcut[]
}

export function KeyboardShortcutsOverlay({
  isVisible,
  onClose,
  shortcuts
}: KeyboardShortcutsOverlayProps) {
  const categories = [...new Set(shortcuts.map(s => s.category))]
  
  if (!isVisible) return null
  
  return (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
      <div className="glass-modal max-w-4xl w-full max-h-[80vh] overflow-hidden rounded-xl">
        {/* Header */}
        <div className="p-6 border-b border-border-subtle">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Keyboard className="w-6 h-6 text-primary" />
              <h2 className="text-xl font-semibold text-text-primary">
                Keyboard Shortcuts
              </h2>
            </div>
            <button
              onClick={onClose}
              className="text-text-tertiary hover:text-text-primary transition-colors"
            >
              <X className="w-6 h-6" />
            </button>
          </div>
        </div>
        
        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-96">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {categories.map(category => (
              <div key={category} className="space-y-4">
                <h3 className="font-semibold text-text-primary text-lg capitalize">
                  {category}
                </h3>
                
                <div className="space-y-3">
                  {shortcuts
                    .filter(shortcut => shortcut.category === category)
                    .map((shortcut, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <span className="text-text-secondary text-sm">
                          {shortcut.description}
                        </span>
                        
                        <div className="flex items-center gap-1">
                          {shortcut.keys.map((key, keyIndex) => (
                            <React.Fragment key={keyIndex}>
                              <kbd className="px-2 py-1 text-xs font-mono bg-neutral-100 dark:bg-neutral-800 border border-border-default rounded">
                                {key}
                              </kbd>
                              {keyIndex < shortcut.keys.length - 1 && (
                                <span className="text-text-tertiary text-xs">+</span>
                              )}
                            </React.Fragment>
                          ))}
                        </div>
                      </div>
                    ))}
                </div>
              </div>
            ))}
          </div>
        </div>
        
        {/* Footer */}
        <div className="p-6 border-t border-border-subtle bg-neutral-50 dark:bg-neutral-800">
          <p className="text-sm text-text-tertiary text-center">
            Press <kbd className="px-2 py-1 text-xs font-mono bg-neutral-200 dark:bg-neutral-700 border border-border-default rounded">?</kbd> anytime to view shortcuts
          </p>
        </div>
      </div>
    </div>
  )
}

// Multi-Device Sync Indicator
interface SyncStatus {
  status: 'synced' | 'syncing' | 'error' | 'offline'
  lastSync: Date
  deviceCount: number
}

interface MultiDeviceSyncIndicatorProps {
  syncStatus: SyncStatus
  onSync?: () => void
  className?: string
}

export function MultiDeviceSyncIndicator({
  syncStatus,
  onSync,
  className
}: MultiDeviceSyncIndicatorProps) {
  const getStatusIcon = () => {
    switch (syncStatus.status) {
      case 'synced':
        return <CheckCircle className="w-4 h-4 text-success" />
      case 'syncing':
        return <Sync className="w-4 h-4 text-primary animate-spin" />
      case 'error':
        return <AlertTriangle className="w-4 h-4 text-error" />
      case 'offline':
        return <X className="w-4 h-4 text-neutral-400" />
    }
  }
  
  const getStatusText = () => {
    switch (syncStatus.status) {
      case 'synced':
        return 'All devices synced'
      case 'syncing':
        return 'Syncing...'
      case 'error':
        return 'Sync failed'
      case 'offline':
        return 'Offline'
    }
  }
  
  const getStatusColor = () => {
    switch (syncStatus.status) {
      case 'synced':
        return 'text-success'
      case 'syncing':
        return 'text-primary'
      case 'error':
        return 'text-error'
      case 'offline':
        return 'text-neutral-400'
    }
  }
  
  return (
    <div className={cn("flex items-center gap-2 text-sm", className)}>
      {getStatusIcon()}
      
      <div className="flex items-center gap-2">
        <span className={cn("font-medium", getStatusColor())}>
          {getStatusText()}
        </span>
        
        {syncStatus.status === 'synced' && (
          <span className="text-text-tertiary">
            • {syncStatus.deviceCount} devices
          </span>
        )}
        
        {syncStatus.status !== 'syncing' && onSync && (
          <button
            onClick={onSync}
            className="text-text-tertiary hover:text-text-primary transition-colors"
            title="Force sync"
          >
            <Sync className="w-3 h-3" />
          </button>
        )}
      </div>
      
      {syncStatus.status === 'synced' && (
        <span className="text-xs text-text-tertiary">
          Last sync: {syncStatus.lastSync.toLocaleTimeString()}
        </span>
      )}
    </div>
  )
}

// Intelligent Form Auto-completion
interface AutoCompleteOption {
  value: string
  label: string
  category?: string
  frequency?: number
}

interface IntelligentAutoCompleteProps {
  value: string
  onChange: (value: string) => void
  options: AutoCompleteOption[]
  placeholder?: string
  className?: string
  onSelect?: (option: AutoCompleteOption) => void
}

export function IntelligentAutoComplete({
  value,
  onChange,
  options,
  placeholder,
  className,
  onSelect
}: IntelligentAutoCompleteProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [selectedIndex, setSelectedIndex] = useState(-1)
  const inputRef = useRef<HTMLInputElement>(null)
  
  // Filter and sort options based on input and frequency
  const filteredOptions = options
    .filter(option =>
      option.label.toLowerCase().includes(value.toLowerCase()) ||
      option.value.toLowerCase().includes(value.toLowerCase())
    )
    .sort((a, b) => {
      // Prioritize exact matches
      const aExact = a.label.toLowerCase().startsWith(value.toLowerCase()) ? 1 : 0
      const bExact = b.label.toLowerCase().startsWith(value.toLowerCase()) ? 1 : 0
      if (aExact !== bExact) return bExact - aExact
      
      // Then sort by frequency
      return (b.frequency || 0) - (a.frequency || 0)
    })
    .slice(0, 8) // Limit to 8 suggestions
  
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!isOpen) return
    
    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault()
        setSelectedIndex(prev => 
          prev < filteredOptions.length - 1 ? prev + 1 : prev
        )
        break
      case 'ArrowUp':
        e.preventDefault()
        setSelectedIndex(prev => prev > 0 ? prev - 1 : -1)
        break
      case 'Enter':
        e.preventDefault()
        if (selectedIndex >= 0 && filteredOptions[selectedIndex]) {
          const option = filteredOptions[selectedIndex]
          onChange(option.value)
          onSelect?.(option)
          setIsOpen(false)
          setSelectedIndex(-1)
        }
        break
      case 'Escape':
        setIsOpen(false)
        setSelectedIndex(-1)
        break
    }
  }
  
  const handleOptionClick = (option: AutoCompleteOption) => {
    onChange(option.value)
    onSelect?.(option)
    setIsOpen(false)
    setSelectedIndex(-1)
    inputRef.current?.focus()
  }
  
  return (
    <div className={cn("relative", className)}>
      <input
        ref={inputRef}
        type="text"
        value={value}
        onChange={(e) => {
          onChange(e.target.value)
          setIsOpen(true)
          setSelectedIndex(-1)
        }}
        onFocus={() => setIsOpen(true)}
        onKeyDown={handleKeyDown}
        placeholder={placeholder}
        className="w-full px-4 py-2 border border-border-default rounded-lg bg-surface text-text-primary placeholder:text-text-tertiary focus:outline-none focus:border-primary focus:ring-2 focus:ring-primary/20 transition-all"
      />
      
      {/* Suggestions Dropdown */}
      {isOpen && filteredOptions.length > 0 && (
        <>
          <div 
            className="fixed inset-0 z-10" 
            onClick={() => setIsOpen(false)}
          />
          
          <div className="absolute top-full left-0 right-0 mt-1 bg-surface border border-border-default rounded-lg shadow-lg z-20 max-h-64 overflow-y-auto">
            {filteredOptions.map((option, index) => (
              <button
                key={`${option.value}-${index}`}
                onClick={() => handleOptionClick(option)}
                className={cn(
                  "w-full text-left px-4 py-3 hover:bg-neutral-100 dark:hover:bg-neutral-800 transition-colors flex items-center justify-between",
                  selectedIndex === index && "bg-primary/10 border-l-2 border-l-primary"
                )}
              >
                <div>
                  <div className="font-medium text-text-primary">
                    {option.label}
                  </div>
                  {option.category && (
                    <div className="text-xs text-text-tertiary">
                      {option.category}
                    </div>
                  )}
                </div>
                
                {option.frequency && option.frequency > 1 && (
                  <div className="text-xs text-text-tertiary">
                    Used {option.frequency} times
                  </div>
                )}
              </button>
            ))}
          </div>
        </>
      )}
    </div>
  )
}
