'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/lib/contexts/auth-context'
import { Button } from '@/components/ui/button'
import { EnhancedButton } from '@/components/ui/enhanced-button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Wallet, TrendingUp, PieChart, Target, ArrowRight, Sparkles } from 'lucide-react'
import { ResponsiveContainer, MobileOptimizedCard, TouchFriendlyButtonGroup } from '@/components/ui/responsive-container'
import { Heading, Text, Container, Stack, Surface } from '@/components/ui/design-system'
import { SimpleThemeToggle } from '@/components/theme/theme-provider'

export default function LandingPage() {
  const { user, loading, isOnboardingComplete } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!loading && user) {
      // If user is authenticated, redirect based on onboarding status
      if (isOnboardingComplete) {
        router.push('/dashboard')
      } else {
        router.push('/onboarding')
      }
    }
  }, [user, loading, router, isOnboardingComplete])

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div>
      </div>
    )
  }

  if (user) {
    return null // Will redirect to dashboard
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Theme Toggle */}
        <div className="flex justify-end py-6">
          <SimpleThemeToggle />
        </div>

        {/* Hero Section */}
        <div className="text-center py-12 sm:py-20">
          <div className="space-y-8">
            <div className="space-y-4">
              <h1 className="text-display-large text-text-primary max-w-4xl mx-auto">
                Take Control of Your
                <span className="text-primary"> Financial Future</span>
              </h1>
              <p className="text-body-large text-text-secondary max-w-2xl mx-auto">
                AI-powered personal finance management that helps you track expenses,
                set goals, and make smarter financial decisions with confidence.
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <EnhancedButton
                variant="primary"
                size="lg"
                rightIcon={<ArrowRight className="w-5 h-5" />}
                onClick={() => router.push('/auth/signup')}
              >
                Get Started Free
              </EnhancedButton>

              <EnhancedButton
                variant="secondary"
                size="lg"
                onClick={() => router.push('/auth/signin')}
              >
                Sign In
              </EnhancedButton>
            </div>
          </div>
        </div>

        {/* Features Grid */}
        <div className="py-16 sm:py-24">
          <div className="text-center mb-16">
            <h2 className="text-heading-large text-text-primary mb-4">
              Everything you need to manage your finances
            </h2>
            <p className="text-body-large text-text-secondary max-w-2xl mx-auto">
              Powerful features designed to simplify your financial life and help you achieve your goals.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Smart Analytics */}
            <Card className="card-base card-interactive">
              <CardContent className="card-content text-center">
                <div className="space-y-4">
                  <div className="w-16 h-16 mx-auto bg-success/10 rounded-xl flex items-center justify-center">
                    <TrendingUp className="icon icon-xl text-success" />
                  </div>
                  <div className="space-y-2">
                    <h3 className="text-heading-medium text-text-primary">Smart Analytics</h3>
                    <p className="text-body-medium text-text-secondary">
                      AI-powered insights into your spending patterns and financial health
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Automatic Categorization */}
            <Card className="card-base card-interactive">
              <CardContent className="card-content text-center">
                <div className="space-y-4">
                  <div className="w-16 h-16 mx-auto bg-primary/10 rounded-xl flex items-center justify-center">
                    <PieChart className="icon icon-xl text-primary" />
                  </div>
                  <div className="space-y-2">
                    <h3 className="text-heading-medium text-text-primary">Auto Categorization</h3>
                    <p className="text-body-medium text-text-secondary">
                      Upload receipts and let AI automatically categorize your expenses
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Goal Tracking */}
            <Card className="card-base card-interactive">
              <CardContent className="card-content text-center">
                <div className="space-y-4">
                  <div className="w-16 h-16 mx-auto bg-warning/10 rounded-xl flex items-center justify-center">
                    <Target className="icon icon-xl text-warning" />
                  </div>
                  <div className="space-y-2">
                    <h3 className="text-heading-medium text-text-primary">Goal Tracking</h3>
                    <p className="text-body-medium text-text-secondary">
                      Set and track financial goals with progress monitoring and insights
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Secure & Private */}
            <Card className="card-base card-interactive">
              <CardContent className="card-content text-center">
                <div className="space-y-4">
                  <div className="w-16 h-16 mx-auto bg-info/10 rounded-xl flex items-center justify-center">
                    <Wallet className="icon icon-xl text-info" />
                  </div>
                  <div className="space-y-2">
                    <h3 className="text-heading-medium text-text-primary">Secure & Private</h3>
                    <p className="text-body-medium text-text-secondary">
                      Bank-level security with end-to-end encryption for your data
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Call to Action */}
        <div className="text-center py-16 sm:py-20">
          <div className="space-y-8">
            <div className="space-y-4">
              <h2 className="text-display-medium text-text-primary">
                Ready to transform your finances?
              </h2>
              <p className="text-body-large text-text-secondary max-w-xl mx-auto">
                Join thousands of users who have taken control of their financial future.
              </p>
            </div>

            <EnhancedButton
              variant="primary"
              size="lg"
              leftIcon={<Sparkles className="w-5 h-5" />}
              onClick={() => router.push('/auth/signup')}
            >
              Start Your Journey
            </EnhancedButton>
          </div>
        </div>
      </div>
    </div>
  )
}
