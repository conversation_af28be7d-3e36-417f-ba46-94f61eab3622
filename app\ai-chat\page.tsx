'use client'

import { useState, useEffect } from 'react'
import { AppLayout } from '@/components/layouts/app-layout'
import { useAuth } from '@/lib/contexts/auth-context'
import {
  LazyFinancialChat,
  LazyReceiptProcessor,
  LazyInsights,
  preloadAIComponents
} from '@/components/ai-chat/lazy-ai-components'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Bot, Receipt, TrendingUp } from 'lucide-react'
import { ErrorBoundary } from '@/components/error-boundary'

export default function AIChatPage() {
  const { user, profile } = useAuth()
  const [activeTab, setActiveTab] = useState('chat')

  // Preload AI components for better performance
  useEffect(() => {
    preloadAIComponents()
  }, [])

  return (
    <AppLayout>
      <div className="max-w-6xl mx-auto space-y-6">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-3xl font-bold tracking-tight">AI Financial Assistant</h1>
          <p className="text-muted-foreground mt-2">
            Get personalized financial advice, process receipts automatically, and discover insights about your spending
          </p>
        </div>

        {/* Feature Overview Cards */}
        <div className="grid md:grid-cols-3 gap-6">
          <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => setActiveTab('chat')}>
            <CardHeader>
              <Bot className="h-8 w-8 text-blue-600 mb-2" />
              <CardTitle className="text-lg">Financial Advisor</CardTitle>
              <CardDescription>
                Get personalized financial advice based on your spending patterns and goals
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => setActiveTab('receipts')}>
            <CardHeader>
              <Receipt className="h-8 w-8 text-green-600 mb-2" />
              <CardTitle className="text-lg">Receipt Processing</CardTitle>
              <CardDescription>
                Upload receipts and automatically extract transaction details with OCR
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => setActiveTab('insights')}>
            <CardHeader>
              <TrendingUp className="h-8 w-8 text-purple-600 mb-2" />
              <CardTitle className="text-lg">Smart Insights</CardTitle>
              <CardDescription>
                Discover spending patterns and get AI-powered financial insights
              </CardDescription>
            </CardHeader>
          </Card>
        </div>

        {/* Main Interface */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="chat">Financial Advisor</TabsTrigger>
            <TabsTrigger value="receipts">Receipt Processing</TabsTrigger>
            <TabsTrigger value="insights">Smart Insights</TabsTrigger>
          </TabsList>

          <TabsContent value="chat">
            <LazyFinancialChat user={user} profile={profile} />
          </TabsContent>

          <TabsContent value="receipts">
            <LazyReceiptProcessor user={user} profile={profile} />
          </TabsContent>

          <TabsContent value="insights">
            <LazyInsights />
          </TabsContent>
        </Tabs>
      </div>
    </AppLayout>
  )
}
