'use client'

import * as React from 'react'
import { createContext, useContext, useEffect, useState } from 'react'

type Theme = 'dark' | 'light' | 'system'

type ThemeProviderProps = {
  children: React.ReactNode
  defaultTheme?: Theme
  storageKey?: string
  attribute?: string
  enableSystem?: boolean
  disableTransitionOnChange?: boolean
}

type ThemeProviderState = {
  theme: Theme
  setTheme: (theme: Theme) => void
  systemTheme: 'dark' | 'light'
  resolvedTheme: 'dark' | 'light'
}

const initialState: ThemeProviderState = {
  theme: 'system',
  setTheme: () => null,
  systemTheme: 'light',
  resolvedTheme: 'light',
}

const ThemeProviderContext = createContext<ThemeProviderState>(initialState)

export function ThemeProvider({
  children,
  defaultTheme = 'system',
  storageKey = 'finance-tracker-theme',
  attribute = 'class',
  enableSystem = true,
  disableTransitionOnChange = false,
  ...props
}: ThemeProviderProps) {
  // Error boundary state
  const [hasError, setHasError] = useState(false)
  const [theme, setTheme] = useState<Theme>(() => {
    if (typeof window !== 'undefined') {
      try {
        return (localStorage.getItem(storageKey) as Theme) || defaultTheme
      } catch (error) {
        console.warn('Failed to read theme from localStorage:', error)
        return defaultTheme
      }
    }
    return defaultTheme
  })

  const [systemTheme, setSystemTheme] = useState<'dark' | 'light'>('light')
  const [mounted, setMounted] = useState(false)

  // Get system theme preference
  useEffect(() => {
    try {
      if (typeof window === 'undefined') return

      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
      setSystemTheme(mediaQuery.matches ? 'dark' : 'light')

      const handleChange = (e: MediaQueryListEvent) => {
        setSystemTheme(e.matches ? 'dark' : 'light')
      }

      mediaQuery.addEventListener('change', handleChange)
      return () => mediaQuery.removeEventListener('change', handleChange)
    } catch (error) {
      console.warn('Failed to setup system theme detection:', error)
      setHasError(true)
    }
  }, [])

  // Apply theme to document
  useEffect(() => {
    if (typeof window === 'undefined' || !window.document) return

    try {
      const root = window.document.documentElement
      const resolvedTheme = theme === 'system' ? systemTheme : theme

    // Disable transitions during theme change if requested
    if (disableTransitionOnChange) {
      try {
        const css = document.createElement('style')
        css.type = 'text/css'
        css.innerHTML = `*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}`

        if (document.head && document.head.appendChild) {
          document.head.appendChild(css)

          // Force reflow
          (() => window.getComputedStyle(document.body))()

          // Remove after a frame
          requestAnimationFrame(() => {
            requestAnimationFrame(() => {
              if (css.parentNode && document.head.contains(css)) {
                document.head.removeChild(css)
              }
            })
          })
        }
      } catch (error) {
        console.warn('Failed to disable transitions during theme change:', error)
      }
    }

    // Remove previous theme classes
    root.classList.remove('light', 'dark')
    
    if (attribute === 'class') {
      root.classList.add(resolvedTheme)
    } else {
      root.setAttribute(attribute, resolvedTheme)
    }

    // Update meta theme-color
    try {
      if (typeof document !== 'undefined') {
        let metaThemeColor = document.querySelector('meta[name="theme-color"]')
        if (!metaThemeColor) {
          metaThemeColor = document.createElement('meta')
          metaThemeColor.setAttribute('name', 'theme-color')
          document.head?.appendChild(metaThemeColor)
        }
        if (metaThemeColor) {
          metaThemeColor.setAttribute(
            'content',
            resolvedTheme === 'dark' ? '#0a0a0a' : '#ffffff'
          )
        }
      }
    } catch (error) {
      console.warn('Failed to update theme-color meta tag:', error)
    }

      setMounted(true)
    } catch (error) {
      console.error('Failed to apply theme:', error)
      setHasError(true)
    }
  }, [theme, systemTheme, attribute, disableTransitionOnChange])

  const handleSetTheme = (newTheme: Theme) => {
    try {
      localStorage.setItem(storageKey, newTheme)
      setTheme(newTheme)
    } catch (error) {
      console.warn('Failed to save theme to localStorage:', error)
      setTheme(newTheme) // Still update the state even if localStorage fails
    }
  }

  const resolvedTheme = theme === 'system' ? systemTheme : theme

  const value = {
    theme,
    setTheme: handleSetTheme,
    systemTheme,
    resolvedTheme,
  }

  // Error fallback
  if (hasError) {
    console.warn('ThemeProvider encountered an error, falling back to light theme')
    return <div className="light">{children}</div>
  }

  // Prevent hydration mismatch
  if (!mounted) {
    return <div style={{ visibility: 'hidden' }}>{children}</div>
  }

  return (
    <ThemeProviderContext.Provider {...props} value={value}>
      {children}
    </ThemeProviderContext.Provider>
  )
}

export const useTheme = () => {
  const context = useContext(ThemeProviderContext)

  if (context === undefined)
    throw new Error('useTheme must be used within a ThemeProvider')

  return context
}

// Theme toggle component
export function ThemeToggle() {
  const { theme, setTheme, resolvedTheme } = useTheme()
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) {
    return (
      <button className="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background hover:bg-accent hover:text-accent-foreground h-10 w-10">
        <span className="sr-only">Toggle theme</span>
        <div className="h-4 w-4" />
      </button>
    )
  }

  return (
    <div className="flex items-center space-x-2">
      <button
        onClick={() => setTheme('light')}
        className={`inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background hover:bg-accent hover:text-accent-foreground h-10 w-10 ${
          theme === 'light' ? 'bg-accent text-accent-foreground' : ''
        }`}
        aria-label="Light theme"
      >
        <SunIcon className="h-4 w-4" />
      </button>
      
      <button
        onClick={() => setTheme('system')}
        className={`inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background hover:bg-accent hover:text-accent-foreground h-10 w-10 ${
          theme === 'system' ? 'bg-accent text-accent-foreground' : ''
        }`}
        aria-label="System theme"
      >
        <MonitorIcon className="h-4 w-4" />
      </button>
      
      <button
        onClick={() => setTheme('dark')}
        className={`inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background hover:bg-accent hover:text-accent-foreground h-10 w-10 ${
          theme === 'dark' ? 'bg-accent text-accent-foreground' : ''
        }`}
        aria-label="Dark theme"
      >
        <MoonIcon className="h-4 w-4" />
      </button>
    </div>
  )
}

// Simple theme toggle button
export function SimpleThemeToggle() {
  const { resolvedTheme, setTheme } = useTheme()
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) {
    return (
      <button className="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background hover:bg-accent hover:text-accent-foreground h-10 w-10">
        <span className="sr-only">Toggle theme</span>
        <div className="h-4 w-4" />
      </button>
    )
  }

  return (
    <button
      onClick={() => setTheme(resolvedTheme === 'dark' ? 'light' : 'dark')}
      className="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background hover:bg-accent hover:text-accent-foreground h-10 w-10"
      aria-label={`Switch to ${resolvedTheme === 'dark' ? 'light' : 'dark'} theme`}
    >
      {resolvedTheme === 'dark' ? (
        <SunIcon className="h-4 w-4" />
      ) : (
        <MoonIcon className="h-4 w-4" />
      )}
    </button>
  )
}

// Icons
function SunIcon({ className }: { className?: string }) {
  return (
    <svg
      className={className}
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
    >
      <circle cx="12" cy="12" r="5" />
      <path d="M12 1v2M12 21v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M1 12h2M21 12h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42" />
    </svg>
  )
}

function MoonIcon({ className }: { className?: string }) {
  return (
    <svg
      className={className}
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z" />
    </svg>
  )
}

function MonitorIcon({ className }: { className?: string }) {
  return (
    <svg
      className={className}
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect x="2" y="3" width="20" height="14" rx="2" ry="2" />
      <path d="M8 21h8M12 17v4" />
    </svg>
  )
}
