'use client'

import React, { useState, useEffect } from 'react'
import { HelpCircle, X, ChevronRight, Lightbulb, BookOpen, Video } from 'lucide-react'
import { cn } from '@/lib/utils'

interface HelpStep {
  id: string
  title: string
  content: string
  target?: string
  position?: 'top' | 'bottom' | 'left' | 'right'
  action?: {
    label: string
    onClick: () => void
  }
}

interface ContextualHelpProps {
  steps: HelpStep[]
  isVisible: boolean
  onClose: () => void
  onComplete?: () => void
  className?: string
}

export function ContextualHelp({ 
  steps, 
  isVisible, 
  onClose, 
  onComplete,
  className 
}: ContextualHelpProps) {
  const [currentStep, setCurrentStep] = useState(0)
  const [targetElement, setTargetElement] = useState<HTMLElement | null>(null)
  
  const step = steps[currentStep]
  
  // Find target element
  useEffect(() => {
    if (step?.target) {
      const element = document.querySelector(step.target) as HTMLElement
      setTargetElement(element)
      
      // Scroll target into view
      if (element) {
        element.scrollIntoView({ 
          behavior: 'smooth', 
          block: 'center' 
        })
        
        // Add highlight class
        element.classList.add('help-highlight')
        
        return () => {
          element.classList.remove('help-highlight')
        }
      }
    }
  }, [step])
  
  const nextStep = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1)
    } else {
      onComplete?.()
      onClose()
    }
  }
  
  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1)
    }
  }
  
  const skipTour = () => {
    onClose()
  }
  
  if (!isVisible || !step) return null
  
  return (
    <>
      {/* Overlay */}
      <div className="fixed inset-0 bg-black/50 z-50 transition-opacity duration-300" />
      
      {/* Help Tooltip */}
      <div 
        className={cn(
          "fixed z-50 bg-white dark:bg-neutral-800 rounded-lg shadow-xl border border-border-default max-w-sm p-4 transition-all duration-300",
          className
        )}
        style={{
          top: targetElement ? getTooltipPosition(targetElement, step.position).top : '50%',
          left: targetElement ? getTooltipPosition(targetElement, step.position).left : '50%',
          transform: targetElement ? 'none' : 'translate(-50%, -50%)'
        }}
      >
        {/* Header */}
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-center gap-2">
            <Lightbulb className="w-5 h-5 text-primary" />
            <h3 className="font-semibold text-text-primary">{step.title}</h3>
          </div>
          <button
            onClick={onClose}
            className="text-text-tertiary hover:text-text-primary transition-colors p-1 -m-1"
            aria-label="Close help"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
        
        {/* Content */}
        <div className="text-text-secondary text-sm mb-4 leading-relaxed">
          {step.content}
        </div>
        
        {/* Action Button */}
        {step.action && (
          <button
            onClick={step.action.onClick}
            className="w-full mb-4 px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-600 transition-colors text-sm font-medium"
          >
            {step.action.label}
          </button>
        )}
        
        {/* Navigation */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <span className="text-xs text-text-tertiary">
              {currentStep + 1} of {steps.length}
            </span>
            <div className="flex gap-1">
              {steps.map((_, index) => (
                <div
                  key={index}
                  className={cn(
                    "w-2 h-2 rounded-full transition-colors",
                    index === currentStep ? "bg-primary" : "bg-neutral-300"
                  )}
                />
              ))}
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <button
              onClick={skipTour}
              className="text-xs text-text-tertiary hover:text-text-secondary transition-colors"
            >
              Skip
            </button>
            
            {currentStep > 0 && (
              <button
                onClick={prevStep}
                className="px-3 py-1 text-xs border border-border-default rounded hover:bg-neutral-100 transition-colors"
              >
                Back
              </button>
            )}
            
            <button
              onClick={nextStep}
              className="px-3 py-1 text-xs bg-primary text-white rounded hover:bg-primary-600 transition-colors flex items-center gap-1"
            >
              {currentStep === steps.length - 1 ? 'Finish' : 'Next'}
              {currentStep < steps.length - 1 && <ChevronRight className="w-3 h-3" />}
            </button>
          </div>
        </div>
        
        {/* Arrow */}
        {targetElement && (
          <div 
            className={cn(
              "absolute w-3 h-3 bg-white dark:bg-neutral-800 border border-border-default transform rotate-45",
              getArrowClasses(step.position)
            )}
          />
        )}
      </div>
    </>
  )
}

// Help trigger button
interface HelpTriggerProps {
  onClick: () => void
  className?: string
  size?: 'sm' | 'md' | 'lg'
}

export function HelpTrigger({ onClick, className, size = 'md' }: HelpTriggerProps) {
  const sizeClasses = {
    sm: 'w-6 h-6',
    md: 'w-8 h-8',
    lg: 'w-10 h-10'
  }
  
  return (
    <button
      onClick={onClick}
      className={cn(
        "inline-flex items-center justify-center rounded-full bg-primary/10 text-primary hover:bg-primary/20 transition-colors focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2",
        sizeClasses[size],
        className
      )}
      aria-label="Get help"
    >
      <HelpCircle className="w-4 h-4" />
    </button>
  )
}

// Feature introduction component
interface FeatureIntroProps {
  title: string
  description: string
  features: string[]
  onGetStarted: () => void
  onSkip: () => void
  isVisible: boolean
}

export function FeatureIntro({ 
  title, 
  description, 
  features, 
  onGetStarted, 
  onSkip, 
  isVisible 
}: FeatureIntroProps) {
  if (!isVisible) return null
  
  return (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
      <div className="bg-white dark:bg-neutral-800 rounded-xl shadow-2xl max-w-md w-full p-6">
        <div className="text-center mb-6">
          <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
            <Lightbulb className="w-8 h-8 text-primary" />
          </div>
          <h2 className="text-xl font-bold text-text-primary mb-2">{title}</h2>
          <p className="text-text-secondary">{description}</p>
        </div>
        
        <div className="space-y-3 mb-6">
          {features.map((feature, index) => (
            <div key={index} className="flex items-start gap-3">
              <div className="w-5 h-5 bg-success/10 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <div className="w-2 h-2 bg-success rounded-full" />
              </div>
              <span className="text-sm text-text-secondary">{feature}</span>
            </div>
          ))}
        </div>
        
        <div className="flex gap-3">
          <button
            onClick={onSkip}
            className="flex-1 px-4 py-2 border border-border-default rounded-lg hover:bg-neutral-100 transition-colors text-sm font-medium"
          >
            Skip for now
          </button>
          <button
            onClick={onGetStarted}
            className="flex-1 px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-600 transition-colors text-sm font-medium"
          >
            Get Started
          </button>
        </div>
      </div>
    </div>
  )
}

// Utility functions
function getTooltipPosition(element: HTMLElement, position: string = 'bottom') {
  const rect = element.getBoundingClientRect()
  const tooltipWidth = 320 // Approximate tooltip width
  const tooltipHeight = 200 // Approximate tooltip height
  const offset = 12
  
  switch (position) {
    case 'top':
      return {
        top: rect.top - tooltipHeight - offset,
        left: rect.left + (rect.width - tooltipWidth) / 2
      }
    case 'bottom':
      return {
        top: rect.bottom + offset,
        left: rect.left + (rect.width - tooltipWidth) / 2
      }
    case 'left':
      return {
        top: rect.top + (rect.height - tooltipHeight) / 2,
        left: rect.left - tooltipWidth - offset
      }
    case 'right':
      return {
        top: rect.top + (rect.height - tooltipHeight) / 2,
        left: rect.right + offset
      }
    default:
      return {
        top: rect.bottom + offset,
        left: rect.left + (rect.width - tooltipWidth) / 2
      }
  }
}

function getArrowClasses(position: string = 'bottom') {
  switch (position) {
    case 'top':
      return 'bottom-[-6px] left-1/2 -translate-x-1/2'
    case 'bottom':
      return 'top-[-6px] left-1/2 -translate-x-1/2'
    case 'left':
      return 'right-[-6px] top-1/2 -translate-y-1/2'
    case 'right':
      return 'left-[-6px] top-1/2 -translate-y-1/2'
    default:
      return 'top-[-6px] left-1/2 -translate-x-1/2'
  }
}
