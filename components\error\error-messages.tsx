import { Alert, AlertDescription } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { AlertTriangle, RefreshCw, Wifi, Database, Key, Clock } from 'lucide-react'

export interface ErrorMessageProps {
  error: Error | string
  onRetry?: () => void
  className?: string
}

// Generic error message component
export function ErrorMessage({ error, onRetry, className }: ErrorMessageProps) {
  const errorMessage = typeof error === 'string' ? error : error.message
  
  return (
    <Alert variant="destructive" className={className}>
      <AlertTriangle className="w-4 h-4" />
      <AlertDescription className="flex items-center justify-between">
        <span>{errorMessage}</span>
        {onRetry && (
          <Button 
            variant="outline" 
            size="sm" 
            onClick={onRetry}
            className="ml-2"
          >
            <RefreshCw className="w-3 h-3 mr-1" />
            Retry
          </Button>
        )}
      </AlertDescription>
    </Alert>
  )
}

// Network error component
export function NetworkError({ onRetry, className }: { onRetry?: () => void; className?: string }) {
  return (
    <Alert variant="destructive" className={className}>
      <Wifi className="w-4 h-4" />
      <AlertDescription className="flex items-center justify-between">
        <div>
          <div className="font-medium">Network Error</div>
          <div className="text-sm">Please check your internet connection and try again.</div>
        </div>
        {onRetry && (
          <Button variant="outline" size="sm" onClick={onRetry}>
            <RefreshCw className="w-3 h-3 mr-1" />
            Retry
          </Button>
        )}
      </AlertDescription>
    </Alert>
  )
}

// Database error component
export function DatabaseError({ onRetry, className }: { onRetry?: () => void; className?: string }) {
  return (
    <Alert variant="destructive" className={className}>
      <Database className="w-4 h-4" />
      <AlertDescription className="flex items-center justify-between">
        <div>
          <div className="font-medium">Database Error</div>
          <div className="text-sm">Unable to connect to the database. Please try again later.</div>
        </div>
        {onRetry && (
          <Button variant="outline" size="sm" onClick={onRetry}>
            <RefreshCw className="w-3 h-3 mr-1" />
            Retry
          </Button>
        )}
      </AlertDescription>
    </Alert>
  )
}

// Authentication error component
export function AuthError({ onRetry, className }: { onRetry?: () => void; className?: string }) {
  return (
    <Alert variant="destructive" className={className}>
      <Key className="w-4 h-4" />
      <AlertDescription className="flex items-center justify-between">
        <div>
          <div className="font-medium">Authentication Error</div>
          <div className="text-sm">Your session has expired. Please sign in again.</div>
        </div>
        {onRetry && (
          <Button variant="outline" size="sm" onClick={onRetry}>
            Sign In
          </Button>
        )}
      </AlertDescription>
    </Alert>
  )
}

// Timeout error component
export function TimeoutError({ onRetry, className }: { onRetry?: () => void; className?: string }) {
  return (
    <Alert variant="destructive" className={className}>
      <Clock className="w-4 h-4" />
      <AlertDescription className="flex items-center justify-between">
        <div>
          <div className="font-medium">Request Timeout</div>
          <div className="text-sm">The request took too long to complete. Please try again.</div>
        </div>
        {onRetry && (
          <Button variant="outline" size="sm" onClick={onRetry}>
            <RefreshCw className="w-3 h-3 mr-1" />
            Retry
          </Button>
        )}
      </AlertDescription>
    </Alert>
  )
}

// Form validation error component
export function FormError({ errors, className }: { errors: string[]; className?: string }) {
  if (errors.length === 0) return null
  
  return (
    <Alert variant="destructive" className={className}>
      <AlertTriangle className="w-4 h-4" />
      <AlertDescription>
        <div className="font-medium">Please fix the following errors:</div>
        <ul className="list-disc list-inside mt-1 space-y-1">
          {errors.map((error, index) => (
            <li key={index} className="text-sm">{error}</li>
          ))}
        </ul>
      </AlertDescription>
    </Alert>
  )
}

// Success message component
export function SuccessMessage({ message, className }: { message: string; className?: string }) {
  return (
    <Alert className={`border-green-200 bg-green-50 text-green-800 ${className}`}>
      <AlertDescription>{message}</AlertDescription>
    </Alert>
  )
}

// Loading error component
export function LoadingError({ 
  resource, 
  onRetry, 
  className 
}: { 
  resource: string
  onRetry?: () => void
  className?: string 
}) {
  return (
    <Alert variant="destructive" className={className}>
      <AlertTriangle className="w-4 h-4" />
      <AlertDescription className="flex items-center justify-between">
        <div>
          <div className="font-medium">Failed to load {resource}</div>
          <div className="text-sm">There was an error loading the data. Please try again.</div>
        </div>
        {onRetry && (
          <Button variant="outline" size="sm" onClick={onRetry}>
            <RefreshCw className="w-3 h-3 mr-1" />
            Retry
          </Button>
        )}
      </AlertDescription>
    </Alert>
  )
}

// Error type detector utility
export function getErrorComponent(error: Error | string, onRetry?: () => void) {
  const errorMessage = typeof error === 'string' ? error : error.message.toLowerCase()
  
  if (errorMessage.includes('network') || errorMessage.includes('fetch')) {
    return <NetworkError onRetry={onRetry} />
  }
  
  if (errorMessage.includes('database') || errorMessage.includes('connection')) {
    return <DatabaseError onRetry={onRetry} />
  }
  
  if (errorMessage.includes('auth') || errorMessage.includes('unauthorized')) {
    return <AuthError onRetry={onRetry} />
  }
  
  if (errorMessage.includes('timeout')) {
    return <TimeoutError onRetry={onRetry} />
  }
  
  return <ErrorMessage error={error} onRetry={onRetry} />
}
