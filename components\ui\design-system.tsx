import * as React from "react"
import { cn } from "@/lib/utils"
import { cva, type VariantProps } from "class-variance-authority"

// Typography Components
const headingVariants = cva(
  "font-bold tracking-tight",
  {
    variants: {
      level: {
        1: "text-4xl sm:text-5xl leading-tight",
        2: "text-3xl sm:text-4xl leading-tight",
        3: "text-2xl sm:text-3xl leading-tight",
        4: "text-xl sm:text-2xl leading-tight",
        5: "text-lg sm:text-xl leading-tight",
        6: "text-base sm:text-lg leading-tight"
      },
      color: {
        default: "text-foreground",
        muted: "text-muted-foreground",
        primary: "text-primary",
        destructive: "text-destructive",
        success: "text-success",
        warning: "text-warning",
        info: "text-info"
      }
    },
    defaultVariants: {
      level: 1,
      color: "default"
    }
  }
)

interface HeadingProps extends React.HTMLAttributes<HTMLHeadingElement>, VariantProps<typeof headingVariants> {
  as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6'
}

export function Heading({ 
  className, 
  level, 
  color, 
  as, 
  children, 
  ...props 
}: HeadingProps) {
  const Component = as || `h${level}` as keyof JSX.IntrinsicElements
  
  return (
    <Component
      className={cn(headingVariants({ level, color, className }))}
      {...props}
    >
      {children}
    </Component>
  )
}

const textVariants = cva(
  "",
  {
    variants: {
      size: {
        xs: "text-xs",
        sm: "text-sm",
        base: "text-base",
        lg: "text-lg",
        xl: "text-xl"
      },
      weight: {
        normal: "font-normal",
        medium: "font-medium",
        semibold: "font-semibold",
        bold: "font-bold"
      },
      color: {
        default: "text-foreground",
        muted: "text-muted-foreground",
        primary: "text-primary",
        destructive: "text-destructive",
        success: "text-success",
        warning: "text-warning",
        info: "text-info"
      },
      leading: {
        tight: "leading-tight",
        normal: "leading-normal",
        relaxed: "leading-relaxed"
      }
    },
    defaultVariants: {
      size: "base",
      weight: "normal",
      color: "default",
      leading: "normal"
    }
  }
)

interface TextProps extends React.HTMLAttributes<HTMLParagraphElement>, VariantProps<typeof textVariants> {
  as?: 'p' | 'span' | 'div' | 'label'
}

export function Text({ 
  className, 
  size, 
  weight, 
  color, 
  leading, 
  as = 'p', 
  children, 
  ...props 
}: TextProps) {
  const Component = as
  
  return (
    <Component
      className={cn(textVariants({ size, weight, color, leading, className }))}
      {...props}
    >
      {children}
    </Component>
  )
}

// Layout Components
const containerVariants = cva(
  "mx-auto w-full",
  {
    variants: {
      size: {
        sm: "max-w-screen-sm",
        md: "max-w-screen-md",
        lg: "max-w-screen-lg",
        xl: "max-w-screen-xl",
        "2xl": "max-w-screen-2xl",
        full: "max-w-full"
      },
      padding: {
        none: "",
        sm: "px-4",
        md: "px-4 sm:px-6",
        lg: "px-4 sm:px-6 lg:px-8"
      }
    },
    defaultVariants: {
      size: "lg",
      padding: "md"
    }
  }
)

interface ContainerProps extends React.HTMLAttributes<HTMLDivElement>, VariantProps<typeof containerVariants> {}

export function Container({ className, size, padding, ...props }: ContainerProps) {
  return (
    <div
      className={cn(containerVariants({ size, padding, className }))}
      {...props}
    />
  )
}

const stackVariants = cva(
  "flex",
  {
    variants: {
      direction: {
        row: "flex-row",
        column: "flex-col"
      },
      spacing: {
        0: "gap-0",
        1: "gap-1",
        2: "gap-2",
        3: "gap-3",
        4: "gap-4",
        5: "gap-5",
        6: "gap-6",
        8: "gap-8",
        10: "gap-10",
        12: "gap-12"
      },
      align: {
        start: "items-start",
        center: "items-center",
        end: "items-end",
        stretch: "items-stretch"
      },
      justify: {
        start: "justify-start",
        center: "justify-center",
        end: "justify-end",
        between: "justify-between",
        around: "justify-around",
        evenly: "justify-evenly"
      }
    },
    defaultVariants: {
      direction: "column",
      spacing: 4,
      align: "stretch",
      justify: "start"
    }
  }
)

interface StackProps extends React.HTMLAttributes<HTMLDivElement>, VariantProps<typeof stackVariants> {}

export function Stack({ className, direction, spacing, align, justify, ...props }: StackProps) {
  return (
    <div
      className={cn(stackVariants({ direction, spacing, align, justify, className }))}
      {...props}
    />
  )
}

// Surface Components
const surfaceVariants = cva(
  "rounded-lg",
  {
    variants: {
      variant: {
        default: "bg-card text-card-foreground border border-border",
        elevated: "bg-card text-card-foreground shadow-md border border-border",
        outlined: "bg-transparent border-2 border-border",
        filled: "bg-muted text-muted-foreground"
      },
      padding: {
        none: "",
        sm: "p-3",
        md: "p-4 sm:p-6",
        lg: "p-6 sm:p-8"
      }
    },
    defaultVariants: {
      variant: "default",
      padding: "md"
    }
  }
)

interface SurfaceProps extends React.HTMLAttributes<HTMLDivElement>, VariantProps<typeof surfaceVariants> {}

export function Surface({ className, variant, padding, ...props }: SurfaceProps) {
  return (
    <div
      className={cn(surfaceVariants({ variant, padding, className }))}
      {...props}
    />
  )
}

// Status Components
const statusVariants = cva(
  "inline-flex items-center gap-1.5 px-2.5 py-1 rounded-full text-xs font-medium",
  {
    variants: {
      variant: {
        default: "bg-secondary text-secondary-foreground",
        primary: "bg-primary text-primary-foreground",
        success: "bg-success text-success-foreground",
        warning: "bg-warning text-warning-foreground",
        destructive: "bg-destructive text-destructive-foreground",
        info: "bg-info text-info-foreground"
      }
    },
    defaultVariants: {
      variant: "default"
    }
  }
)

interface StatusBadgeProps extends React.HTMLAttributes<HTMLSpanElement>, VariantProps<typeof statusVariants> {
  icon?: React.ReactNode
}

export function StatusBadge({ className, variant, icon, children, ...props }: StatusBadgeProps) {
  return (
    <span
      className={cn(statusVariants({ variant, className }))}
      {...props}
    >
      {icon}
      {children}
    </span>
  )
}

// Export design tokens for use in other components
export const designTokens = {
  spacing: {
    0: 'var(--spacing-0)',
    1: 'var(--spacing-1)',
    2: 'var(--spacing-2)',
    3: 'var(--spacing-3)',
    4: 'var(--spacing-4)',
    5: 'var(--spacing-5)',
    6: 'var(--spacing-6)',
    8: 'var(--spacing-8)',
    10: 'var(--spacing-10)',
    12: 'var(--spacing-12)',
    16: 'var(--spacing-16)',
    20: 'var(--spacing-20)',
    24: 'var(--spacing-24)'
  },
  fontSize: {
    xs: 'var(--font-size-xs)',
    sm: 'var(--font-size-sm)',
    base: 'var(--font-size-base)',
    lg: 'var(--font-size-lg)',
    xl: 'var(--font-size-xl)',
    '2xl': 'var(--font-size-2xl)',
    '3xl': 'var(--font-size-3xl)',
    '4xl': 'var(--font-size-4xl)',
    '5xl': 'var(--font-size-5xl)'
  },
  borderRadius: {
    none: 'var(--radius-none)',
    sm: 'var(--radius-sm)',
    md: 'var(--radius-md)',
    lg: 'var(--radius-lg)',
    xl: 'var(--radius-xl)',
    '2xl': 'var(--radius-2xl)',
    full: 'var(--radius-full)'
  },
  boxShadow: {
    sm: 'var(--shadow-sm)',
    md: 'var(--shadow-md)',
    lg: 'var(--shadow-lg)',
    xl: 'var(--shadow-xl)'
  }
}
