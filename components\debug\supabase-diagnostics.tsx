'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  Database, 
  Key, 
  Globe,
  RefreshCw,
  Info
} from 'lucide-react'
import { supabase, testSupabaseConnection } from '@/lib/supabase/client'
import { useAuth } from '@/lib/contexts/auth-context'
import { log } from '@/lib/utils/logger'

interface DiagnosticResult {
  name: string
  status: 'success' | 'error' | 'warning' | 'info'
  message: string
  details?: any
}

export function SupabaseDiagnostics() {
  const { user, profile } = useAuth()
  const [diagnostics, setDiagnostics] = useState<DiagnosticResult[]>([])
  const [isRunning, setIsRunning] = useState(false)

  useEffect(() => {
    runDiagnostics()
  }, [])

  const runDiagnostics = async () => {
    setIsRunning(true)
    const results: DiagnosticResult[] = []

    try {
      // 1. Environment Variables Check
      const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
      const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

      if (!supabaseUrl) {
        results.push({
          name: 'Supabase URL',
          status: 'error',
          message: 'NEXT_PUBLIC_SUPABASE_URL environment variable is missing'
        })
      } else {
        try {
          new URL(supabaseUrl)
          results.push({
            name: 'Supabase URL',
            status: 'success',
            message: 'Environment variable is set and valid',
            details: { url: supabaseUrl.substring(0, 30) + '...' }
          })
        } catch {
          results.push({
            name: 'Supabase URL',
            status: 'error',
            message: 'Invalid URL format',
            details: { url: supabaseUrl }
          })
        }
      }

      if (!supabaseAnonKey) {
        results.push({
          name: 'Supabase Anon Key',
          status: 'error',
          message: 'NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable is missing'
        })
      } else {
        results.push({
          name: 'Supabase Anon Key',
          status: 'success',
          message: 'Environment variable is set',
          details: { keyLength: supabaseAnonKey.length }
        })
      }

      // 2. Connection Test
      try {
        const connectionTest = await testSupabaseConnection()
        if (connectionTest) {
          results.push({
            name: 'Database Connection',
            status: 'success',
            message: 'Successfully connected to Supabase'
          })
        } else {
          results.push({
            name: 'Database Connection',
            status: 'error',
            message: 'Failed to connect to Supabase database'
          })
        }
      } catch (error) {
        results.push({
          name: 'Database Connection',
          status: 'error',
          message: 'Connection test failed',
          details: { error: error instanceof Error ? error.message : String(error) }
        })
      }

      // 3. Authentication Status
      if (user) {
        results.push({
          name: 'User Authentication',
          status: 'success',
          message: 'User is authenticated',
          details: { userId: user.id, email: user.email }
        })

        if (profile) {
          results.push({
            name: 'User Profile',
            status: 'success',
            message: 'User profile loaded successfully',
            details: { 
              fullName: profile.full_name,
              currency: profile.currency_code
            }
          })
        } else {
          results.push({
            name: 'User Profile',
            status: 'warning',
            message: 'User profile not loaded or missing'
          })
        }
      } else {
        results.push({
          name: 'User Authentication',
          status: 'warning',
          message: 'User is not authenticated'
        })
      }

      // 4. Test Basic Query
      if (user) {
        try {
          const { data, error } = await supabase
            .from('transactions')
            .select('count')
            .eq('user_id', user.id)
            .limit(1)

          if (error) {
            results.push({
              name: 'Transaction Query Test',
              status: 'error',
              message: 'Failed to query transactions table',
              details: { 
                error: error.message,
                code: error.code,
                hint: error.hint
              }
            })
          } else {
            results.push({
              name: 'Transaction Query Test',
              status: 'success',
              message: 'Successfully queried transactions table'
            })
          }
        } catch (error) {
          results.push({
            name: 'Transaction Query Test',
            status: 'error',
            message: 'Query test failed with exception',
            details: { error: error instanceof Error ? error.message : String(error) }
          })
        }
      }

      // 5. RLS (Row Level Security) Test
      if (user) {
        try {
          const { data, error } = await supabase
            .from('user_profiles')
            .select('id')
            .eq('id', user.id)
            .single()

          if (error) {
            results.push({
              name: 'RLS Policy Test',
              status: 'error',
              message: 'Row Level Security policy may be blocking access',
              details: { 
                error: error.message,
                code: error.code
              }
            })
          } else {
            results.push({
              name: 'RLS Policy Test',
              status: 'success',
              message: 'Row Level Security policies are working correctly'
            })
          }
        } catch (error) {
          results.push({
            name: 'RLS Policy Test',
            status: 'error',
            message: 'RLS test failed',
            details: { error: error instanceof Error ? error.message : String(error) }
          })
        }
      }

    } catch (error) {
      results.push({
        name: 'Diagnostic Error',
        status: 'error',
        message: 'Failed to run diagnostics',
        details: { error: error instanceof Error ? error.message : String(error) }
      })
    }

    setDiagnostics(results)
    setIsRunning(false)

    // Log results for debugging
    log.info('Supabase diagnostics completed', { results }, 'SUPABASE_DIAGNOSTICS')
  }

  const getStatusIcon = (status: DiagnosticResult['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-500" />
      case 'error':
        return <XCircle className="w-4 h-4 text-red-500" />
      case 'warning':
        return <AlertTriangle className="w-4 h-4 text-yellow-500" />
      case 'info':
        return <Info className="w-4 h-4 text-blue-500" />
    }
  }

  const getStatusBadge = (status: DiagnosticResult['status']) => {
    switch (status) {
      case 'success':
        return <Badge variant="default" className="bg-green-100 text-green-800">Success</Badge>
      case 'error':
        return <Badge variant="destructive">Error</Badge>
      case 'warning':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Warning</Badge>
      case 'info':
        return <Badge variant="outline">Info</Badge>
    }
  }

  const hasErrors = diagnostics.some(d => d.status === 'error')
  const hasWarnings = diagnostics.some(d => d.status === 'warning')

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Database className="w-5 h-5" />
          Supabase Connection Diagnostics
        </CardTitle>
        <CardDescription>
          Diagnose connection issues and verify Supabase configuration
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center gap-2">
          <Button 
            onClick={runDiagnostics} 
            disabled={isRunning}
            size="sm"
          >
            {isRunning ? (
              <>
                <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                Running Diagnostics...
              </>
            ) : (
              <>
                <RefreshCw className="w-4 h-4 mr-2" />
                Run Diagnostics
              </>
            )}
          </Button>
          
          {diagnostics.length > 0 && (
            <div className="flex gap-2">
              {hasErrors && (
                <Badge variant="destructive">
                  {diagnostics.filter(d => d.status === 'error').length} Errors
                </Badge>
              )}
              {hasWarnings && (
                <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                  {diagnostics.filter(d => d.status === 'warning').length} Warnings
                </Badge>
              )}
            </div>
          )}
        </div>

        {diagnostics.length > 0 && (
          <div className="space-y-3">
            {diagnostics.map((diagnostic, index) => (
              <div key={index} className="border rounded-lg p-3">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    {getStatusIcon(diagnostic.status)}
                    <span className="font-medium">{diagnostic.name}</span>
                  </div>
                  {getStatusBadge(diagnostic.status)}
                </div>
                <p className="text-sm text-gray-600 mb-2">{diagnostic.message}</p>
                {diagnostic.details && (
                  <details className="text-xs">
                    <summary className="cursor-pointer text-gray-500 hover:text-gray-700">
                      Show Details
                    </summary>
                    <pre className="mt-2 p-2 bg-gray-50 rounded text-xs overflow-auto">
                      {JSON.stringify(diagnostic.details, null, 2)}
                    </pre>
                  </details>
                )}
              </div>
            ))}
          </div>
        )}

        {hasErrors && (
          <Alert variant="destructive">
            <XCircle className="w-4 h-4" />
            <AlertDescription>
              <strong>Critical Issues Found:</strong> There are configuration or connection errors 
              that need to be resolved. Check the error details above and ensure your environment 
              variables are correctly set.
            </AlertDescription>
          </Alert>
        )}

        {hasWarnings && !hasErrors && (
          <Alert>
            <AlertTriangle className="w-4 h-4" />
            <AlertDescription>
              <strong>Warnings Found:</strong> Some issues were detected that may affect functionality. 
              Review the warnings above.
            </AlertDescription>
          </Alert>
        )}

        {!hasErrors && !hasWarnings && diagnostics.length > 0 && (
          <Alert>
            <CheckCircle className="w-4 h-4" />
            <AlertDescription>
              <strong>All Systems Operational:</strong> Supabase connection and configuration 
              are working correctly.
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  )
}
