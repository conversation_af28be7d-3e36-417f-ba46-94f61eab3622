'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { EnhancedButton } from '@/components/ui/enhanced-button'
import { 
  BrandLoadingAnimation,
  NoTransactionsIllustration,
  NoGoalsIllustration,
  SuccessCelebrationIllustration,
  ErrorStateIllustration,
  BrandMascot,
  BrandIcon
} from '@/components/brand/brand-illustrations'
import {
  AdvancedDataTable,
  AdvancedProgressRing,
  InteractiveMetricCard,
  SmartDataInsight
} from '@/components/data-visualization/advanced-charts'
import {
  SmartNotificationSystem,
  KeyboardShortcutsOverlay,
  MultiDeviceSyncIndicator,
  IntelligentAutoComplete
} from '@/components/enterprise/advanced-features'
import { 
  <PERSON>rkles, 
  <PERSON>lette, 
  BarChart3, 
  Zap, 
  Crown,
  TrendingUp,
  DollarSign,
  Target,
  CreditCard,
  PiggyBank,
  Wallet,
  Activity,
  Users,
  Shield,
  Keyboard,
  Bell,
  RefreshCw,
  Search,
  Eye,
  Download
} from 'lucide-react'

// Mock data for demonstrations
const mockTransactions = [
  { id: 1, date: '2024-01-15', description: 'Starbucks Coffee', amount: -4.50, category: 'Food & Dining', status: 'completed', trend: 5 },
  { id: 2, date: '2024-01-14', description: 'Salary Deposit', amount: 3500.00, category: 'Income', status: 'completed', trend: 12 },
  { id: 3, date: '2024-01-13', description: 'Grocery Store', amount: -85.32, category: 'Groceries', status: 'completed', trend: -3 },
  { id: 4, date: '2024-01-12', description: 'Gas Station', amount: -45.00, category: 'Transportation', status: 'pending', trend: 0 },
  { id: 5, date: '2024-01-11', description: 'Netflix Subscription', amount: -15.99, category: 'Entertainment', status: 'completed', trend: -2 }
]

const mockNotifications = [
  {
    id: '1',
    title: 'Budget Alert',
    message: 'You\'ve spent 85% of your dining budget this month.',
    type: 'warning' as const,
    priority: 'high' as const,
    timestamp: new Date(Date.now() - 1000 * 60 * 30),
    read: false,
    actionable: true,
    action: { label: 'View Budget', onClick: () => console.log('View budget') }
  },
  {
    id: '2',
    title: 'Goal Achievement',
    message: 'Congratulations! You\'ve reached your emergency fund goal.',
    type: 'success' as const,
    priority: 'medium' as const,
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2),
    read: false
  },
  {
    id: '3',
    title: 'New Feature',
    message: 'Try our new AI-powered spending insights.',
    type: 'info' as const,
    priority: 'low' as const,
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24),
    read: true
  }
]

const keyboardShortcuts = [
  { keys: ['Ctrl', 'K'], description: 'Open command palette', category: 'navigation' },
  { keys: ['G', 'D'], description: 'Go to dashboard', category: 'navigation' },
  { keys: ['G', 'T'], description: 'Go to transactions', category: 'navigation' },
  { keys: ['N', 'T'], description: 'New transaction', category: 'actions' },
  { keys: ['N', 'G'], description: 'New goal', category: 'actions' },
  { keys: ['Ctrl', 'S'], description: 'Save current form', category: 'actions' },
  { keys: ['?'], description: 'Show keyboard shortcuts', category: 'help' },
  { keys: ['Esc'], description: 'Close modal/overlay', category: 'help' }
]

const autoCompleteOptions = [
  { value: 'Starbucks', label: 'Starbucks Coffee', category: 'Food & Dining', frequency: 15 },
  { value: 'Grocery Store', label: 'Whole Foods Market', category: 'Groceries', frequency: 8 },
  { value: 'Gas Station', label: 'Shell Gas Station', category: 'Transportation', frequency: 12 },
  { value: 'Netflix', label: 'Netflix Subscription', category: 'Entertainment', frequency: 1 },
  { value: 'Amazon', label: 'Amazon Purchase', category: 'Shopping', frequency: 25 }
]

export default function PremiumShowcase() {
  const [currentDemo, setCurrentDemo] = useState('visual-effects')
  const [showKeyboardShortcuts, setShowKeyboardShortcuts] = useState(false)
  const [mascotMood, setMascotMood] = useState<'happy' | 'thinking' | 'celebrating' | 'sleeping'>('happy')
  const [autoCompleteValue, setAutoCompleteValue] = useState('')
  const [syncStatus, setSyncStatus] = useState({
    status: 'synced' as const,
    lastSync: new Date(),
    deviceCount: 3
  })

  // Simulate sync status changes
  useEffect(() => {
    const interval = setInterval(() => {
      const statuses = ['synced', 'syncing', 'error', 'offline'] as const
      const randomStatus = statuses[Math.floor(Math.random() * statuses.length)]
      setSyncStatus(prev => ({
        ...prev,
        status: randomStatus,
        lastSync: randomStatus === 'synced' ? new Date() : prev.lastSync
      }))
    }, 10000)

    return () => clearInterval(interval)
  }, [])

  // Keyboard shortcut handler
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === '?' && !e.ctrlKey && !e.metaKey) {
        e.preventDefault()
        setShowKeyboardShortcuts(true)
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [])

  const tableColumns = [
    { key: 'date', label: 'Date', type: 'date', sortable: true },
    { key: 'description', label: 'Description', type: 'text', sortable: true },
    { key: 'amount', label: 'Amount', type: 'currency', sortable: true, align: 'right' as const },
    { key: 'category', label: 'Category', type: 'text', sortable: true },
    { key: 'status', label: 'Status', type: 'status' },
    { key: 'trend', label: 'Trend', type: 'trend', align: 'center' as const }
  ]

  return (
    <div className="min-h-screen bg-background">
      {/* Import advanced styles */}
      <style jsx global>{`
        @import url('/app/advanced-visual-effects.css');
      `}</style>
      
      <div className="max-w-7xl mx-auto p-6 space-y-8">
        {/* Header */}
        <div className="text-center space-y-4 gradient-overlay-primary texture-grain rounded-2xl p-12">
          <div className="flex items-center justify-center gap-4 mb-6">
            <Crown className="w-12 h-12 text-primary animate-pulse" />
            <h1 className="text-display-large text-text-primary">
              Premium Professional Showcase
            </h1>
            <Crown className="w-12 h-12 text-primary animate-pulse" />
          </div>
          
          <p className="text-body-large text-text-secondary max-w-4xl mx-auto">
            Experience enterprise-grade features with advanced visual effects, brand personality, 
            data visualization excellence, and cutting-edge functionality.
          </p>
          
          {/* Navigation Pills */}
          <div className="flex flex-wrap justify-center gap-3 mt-8">
            {[
              { id: 'visual-effects', label: 'Visual Effects', icon: Sparkles },
              { id: 'brand-personality', label: 'Brand Identity', icon: Palette },
              { id: 'data-visualization', label: 'Data Viz', icon: BarChart3 },
              { id: 'enterprise-features', label: 'Enterprise', icon: Shield },
              { id: 'cutting-edge', label: 'Cutting Edge', icon: Zap }
            ].map(({ id, label, icon: Icon }) => (
              <button
                key={id}
                onClick={() => setCurrentDemo(id)}
                className={cn(
                  "inline-flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium transition-all duration-300",
                  currentDemo === id
                    ? "bg-primary text-white shadow-primary elevation-3"
                    : "bg-surface text-text-secondary hover:bg-neutral-100 hover:text-text-primary elevation-1"
                )}
              >
                <Icon className="w-4 h-4" />
                {label}
              </button>
            ))}
          </div>
        </div>

        {/* Demo Content */}
        <div className="space-y-8">
          
          {/* Advanced Visual Effects */}
          {currentDemo === 'visual-effects' && (
            <div className="space-y-8">
              <Card className="card-premium-elevated">
                <CardHeader>
                  <CardTitle className="flex items-center gap-3">
                    <Sparkles className="w-6 h-6 text-primary" />
                    Advanced Visual Effects
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-8">
                  
                  {/* Glassmorphism Examples */}
                  <div className="space-y-4">
                    <h3 className="text-heading-medium">Glassmorphism Effects</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div className="glass-morphism p-6 rounded-xl">
                        <h4 className="font-semibold text-text-primary mb-2">Glass Card</h4>
                        <p className="text-text-secondary text-sm">
                          Beautiful glassmorphism effect with backdrop blur and subtle transparency.
                        </p>
                      </div>
                      
                      <div className="glass-modal p-6 rounded-xl">
                        <h4 className="font-semibold text-text-primary mb-2">Modal Glass</h4>
                        <p className="text-text-secondary text-sm">
                          Enhanced glass effect perfect for modal overlays and important content.
                        </p>
                      </div>
                      
                      <div className="card-premium-glass p-6 rounded-xl">
                        <h4 className="font-semibold text-text-primary mb-2">Premium Glass</h4>
                        <p className="text-text-secondary text-sm">
                          Multi-layered glass effect with gradient overlays and premium styling.
                        </p>
                      </div>
                    </div>
                  </div>
                  
                  {/* Elevation System */}
                  <div className="space-y-4">
                    <h3 className="text-heading-medium">Sophisticated Shadow System</h3>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      {[1, 3, 5, 7].map(level => (
                        <div
                          key={level}
                          className={`p-4 rounded-lg bg-surface text-center elevation-${level} transition-all duration-300 hover:elevation-${level + 1}`}
                        >
                          <div className="text-sm font-medium text-text-primary">
                            Elevation {level}
                          </div>
                          <div className="text-xs text-text-tertiary mt-1">
                            Hover to increase
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                  
                  {/* Texture Effects */}
                  <div className="space-y-4">
                    <h3 className="text-heading-medium">Texture & Depth Effects</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div className="texture-noise p-6 rounded-xl bg-surface border border-border-default">
                        <h4 className="font-semibold text-text-primary mb-2">Noise Texture</h4>
                        <p className="text-text-secondary text-sm">
                          Subtle noise pattern adds depth and premium feel to surfaces.
                        </p>
                      </div>
                      
                      <div className="texture-grain p-6 rounded-xl bg-surface border border-border-default">
                        <h4 className="font-semibold text-text-primary mb-2">Grain Effect</h4>
                        <p className="text-text-secondary text-sm">
                          Film grain texture creates organic, tactile surface quality.
                        </p>
                      </div>
                      
                      <div className="texture-paper p-6 rounded-xl border border-border-default">
                        <h4 className="font-semibold text-text-primary mb-2">Paper Texture</h4>
                        <p className="text-text-secondary text-sm">
                          Paper-like texture with subtle diagonal lines for document feel.
                        </p>
                      </div>
                    </div>
                  </div>
                  
                  {/* Elegant Dividers */}
                  <div className="space-y-6">
                    <h3 className="text-heading-medium">Elegant Dividers</h3>
                    
                    <div className="divider-gradient" />
                    <div className="text-center text-sm text-text-tertiary">Gradient Fade Divider</div>
                    
                    <div className="divider-glow" />
                    <div className="text-center text-sm text-text-tertiary">Glowing Accent Divider</div>
                    
                    <div className="separator-elegant">
                      <span>Section Separator</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Brand Personality */}
          {currentDemo === 'brand-personality' && (
            <div className="space-y-8">
              <Card className="card-premium-floating">
                <CardHeader>
                  <CardTitle className="flex items-center gap-3">
                    <Palette className="w-6 h-6 text-primary" />
                    Brand Personality & Illustrations
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-8">
                  
                  {/* Brand Mascot */}
                  <div className="text-center space-y-6">
                    <h3 className="text-heading-medium">Brand Mascot</h3>
                    <div className="flex justify-center gap-8">
                      {(['happy', 'thinking', 'celebrating', 'sleeping'] as const).map(mood => (
                        <button
                          key={mood}
                          onClick={() => setMascotMood(mood)}
                          className={cn(
                            "p-4 rounded-xl transition-all duration-300",
                            mascotMood === mood 
                              ? "bg-primary/10 border-2 border-primary elevation-3" 
                              : "bg-surface border border-border-default hover:elevation-2"
                          )}
                        >
                          <BrandMascot mood={mood} size={80} />
                          <div className="text-sm font-medium text-text-primary mt-2 capitalize">
                            {mood}
                          </div>
                        </button>
                      ))}
                    </div>
                  </div>
                  
                  {/* Custom Loading Animation */}
                  <div className="text-center space-y-4">
                    <h3 className="text-heading-medium">Brand Loading Animation</h3>
                    <div className="flex justify-center gap-8">
                      <BrandLoadingAnimation size={60} />
                      <BrandLoadingAnimation size={80} />
                      <BrandLoadingAnimation size={100} />
                    </div>
                  </div>
                  
                  {/* Empty State Illustrations */}
                  <div className="space-y-6">
                    <h3 className="text-heading-medium">Branded Empty States</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                      <Card className="p-6 text-center">
                        <NoTransactionsIllustration />
                        <h4 className="font-medium text-text-primary mt-4">No Transactions</h4>
                        <p className="text-sm text-text-secondary mt-2">
                          Start tracking your expenses
                        </p>
                      </Card>
                      
                      <Card className="p-6 text-center">
                        <NoGoalsIllustration />
                        <h4 className="font-medium text-text-primary mt-4">No Goals</h4>
                        <p className="text-sm text-text-secondary mt-2">
                          Set your first financial goal
                        </p>
                      </Card>
                      
                      <Card className="p-6 text-center">
                        <SuccessCelebrationIllustration />
                        <h4 className="font-medium text-text-primary mt-4">Success!</h4>
                        <p className="text-sm text-text-secondary mt-2">
                          Goal achieved with celebration
                        </p>
                      </Card>
                      
                      <Card className="p-6 text-center">
                        <ErrorStateIllustration />
                        <h4 className="font-medium text-text-primary mt-4">Error State</h4>
                        <p className="text-sm text-text-secondary mt-2">
                          Friendly error handling
                        </p>
                      </Card>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Data Visualization */}
          {currentDemo === 'data-visualization' && (
            <div className="space-y-8">
              <Card className="card-premium-elevated">
                <CardHeader>
                  <CardTitle className="flex items-center gap-3">
                    <BarChart3 className="w-6 h-6 text-primary" />
                    Data Visualization Excellence
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-8">
                  
                  {/* Interactive Metric Cards */}
                  <div className="space-y-4">
                    <h3 className="text-heading-medium">Interactive Metric Cards</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                      <InteractiveMetricCard
                        title="Total Balance"
                        value="$12,450.32"
                        change={8.2}
                        changeLabel="vs last month"
                        trend="up"
                        icon={<DollarSign className="w-5 h-5" />}
                        onClick={() => console.log('Balance clicked')}
                      />
                      
                      <InteractiveMetricCard
                        title="Monthly Spending"
                        value="$2,340.50"
                        change={-5.1}
                        changeLabel="vs last month"
                        trend="down"
                        icon={<CreditCard className="w-5 h-5" />}
                        onClick={() => console.log('Spending clicked')}
                      />
                      
                      <InteractiveMetricCard
                        title="Savings Rate"
                        value="23%"
                        change={2.3}
                        changeLabel="vs last month"
                        trend="up"
                        icon={<PiggyBank className="w-5 h-5" />}
                        onClick={() => console.log('Savings clicked')}
                      />
                      
                      <InteractiveMetricCard
                        title="Goals Progress"
                        value="4/6"
                        change={0}
                        changeLabel="goals completed"
                        trend="neutral"
                        icon={<Target className="w-5 h-5" />}
                        onClick={() => console.log('Goals clicked')}
                      />
                    </div>
                  </div>
                  
                  {/* Advanced Progress Rings */}
                  <div className="space-y-4">
                    <h3 className="text-heading-medium">Advanced Progress Indicators</h3>
                    <div className="flex justify-center gap-8">
                      <AdvancedProgressRing
                        value={75}
                        label="Emergency Fund"
                        color="success"
                        animated
                      />
                      <AdvancedProgressRing
                        value={45}
                        label="Vacation Goal"
                        color="primary"
                        animated
                      />
                      <AdvancedProgressRing
                        value={90}
                        label="Debt Payoff"
                        color="warning"
                        animated
                      />
                    </div>
                  </div>
                  
                  {/* Professional Data Table */}
                  <div className="space-y-4">
                    <h3 className="text-heading-medium">Professional Data Table</h3>
                    <AdvancedDataTable
                      columns={tableColumns}
                      data={mockTransactions}
                      onRowClick={(row) => console.log('Row clicked:', row)}
                    />
                  </div>
                  
                  {/* Smart Data Insights */}
                  <div className="space-y-4">
                    <h3 className="text-heading-medium">AI-Powered Insights</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <SmartDataInsight
                        title="Spending Pattern"
                        insight="Your dining expenses have increased by 23% this month. Consider setting a stricter budget or finding more cost-effective alternatives."
                        confidence={87}
                        trend="negative"
                        actionable
                        onAction={() => console.log('Create dining budget')}
                      />
                      
                      <SmartDataInsight
                        title="Savings Opportunity"
                        insight="Based on your income pattern, you could save an additional $340 per month by optimizing your subscription services."
                        confidence={92}
                        trend="positive"
                        actionable
                        onAction={() => console.log('Review subscriptions')}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Enterprise Features */}
          {currentDemo === 'enterprise-features' && (
            <div className="space-y-8">
              <Card className="card-premium-glass">
                <CardHeader>
                  <CardTitle className="flex items-center gap-3">
                    <Shield className="w-6 h-6 text-primary" />
                    Enterprise-Grade Features
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-8">
                  
                  {/* Smart Notifications */}
                  <div className="space-y-4">
                    <h3 className="text-heading-medium">Smart Notification System</h3>
                    <div className="flex justify-center">
                      <SmartNotificationSystem
                        notifications={mockNotifications}
                        onMarkAsRead={(id) => console.log('Mark as read:', id)}
                        onMarkAllAsRead={() => console.log('Mark all as read')}
                        onDismiss={(id) => console.log('Dismiss:', id)}
                      />
                    </div>
                    <p className="text-center text-sm text-text-secondary">
                      Click the notification bell to see priority-based notifications with smart categorization.
                    </p>
                  </div>
                  
                  {/* Multi-Device Sync */}
                  <div className="space-y-4">
                    <h3 className="text-heading-medium">Multi-Device Synchronization</h3>
                    <div className="flex justify-center">
                      <MultiDeviceSyncIndicator
                        syncStatus={syncStatus}
                        onSync={() => {
                          setSyncStatus(prev => ({ ...prev, status: 'syncing' }))
                          setTimeout(() => {
                            setSyncStatus(prev => ({ 
                              ...prev, 
                              status: 'synced', 
                              lastSync: new Date() 
                            }))
                          }, 2000)
                        }}
                      />
                    </div>
                    <p className="text-center text-sm text-text-secondary">
                      Real-time sync status across all your devices with automatic conflict resolution.
                    </p>
                  </div>
                  
                  {/* Keyboard Shortcuts */}
                  <div className="space-y-4">
                    <h3 className="text-heading-medium">Advanced Keyboard Shortcuts</h3>
                    <div className="text-center">
                      <EnhancedButton
                        variant="secondary"
                        onClick={() => setShowKeyboardShortcuts(true)}
                        leftIcon={<Keyboard className="w-4 h-4" />}
                      >
                        View Shortcuts
                      </EnhancedButton>
                      <p className="text-sm text-text-secondary mt-2">
                        Press <kbd className="px-2 py-1 text-xs font-mono bg-neutral-100 border border-border-default rounded">?</kbd> anytime to view shortcuts
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Cutting-Edge Features */}
          {currentDemo === 'cutting-edge' && (
            <div className="space-y-8">
              <Card className="card-premium-floating gradient-overlay-primary">
                <CardHeader>
                  <CardTitle className="flex items-center gap-3">
                    <Zap className="w-6 h-6 text-primary" />
                    Cutting-Edge Features
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-8">
                  
                  {/* Intelligent Auto-Complete */}
                  <div className="space-y-4">
                    <h3 className="text-heading-medium">Intelligent Auto-Complete</h3>
                    <div className="max-w-md mx-auto">
                      <IntelligentAutoComplete
                        value={autoCompleteValue}
                        onChange={setAutoCompleteValue}
                        options={autoCompleteOptions}
                        placeholder="Start typing a merchant name..."
                        onSelect={(option) => console.log('Selected:', option)}
                      />
                    </div>
                    <p className="text-center text-sm text-text-secondary">
                      Smart suggestions based on frequency and context with category recognition.
                    </p>
                  </div>
                  
                  {/* Future-Ready Architecture */}
                  <div className="space-y-4">
                    <h3 className="text-heading-medium">Future-Ready Architecture</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                      <div className="glass-morphism p-6 rounded-xl text-center">
                        <Activity className="w-8 h-8 text-primary mx-auto mb-3" />
                        <h4 className="font-semibold text-text-primary mb-2">Real-time Updates</h4>
                        <p className="text-sm text-text-secondary">
                          Live data synchronization with WebSocket connections
                        </p>
                      </div>
                      
                      <div className="glass-morphism p-6 rounded-xl text-center">
                        <Users className="w-8 h-8 text-primary mx-auto mb-3" />
                        <h4 className="font-semibold text-text-primary mb-2">Collaboration</h4>
                        <p className="text-sm text-text-secondary">
                          Multi-user editing with conflict resolution
                        </p>
                      </div>
                      
                      <div className="glass-morphism p-6 rounded-xl text-center">
                        <Eye className="w-8 h-8 text-primary mx-auto mb-3" />
                        <h4 className="font-semibold text-text-primary mb-2">Personalization</h4>
                        <p className="text-sm text-text-secondary">
                          AI-driven interface adaptation to user behavior
                        </p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </div>
      </div>
      
      {/* Keyboard Shortcuts Overlay */}
      <KeyboardShortcutsOverlay
        isVisible={showKeyboardShortcuts}
        onClose={() => setShowKeyboardShortcuts(false)}
        shortcuts={keyboardShortcuts}
      />
    </div>
  )
}
