#!/usr/bin/env node

/**
 * Find Unused Imports Script
 * Scans TypeScript/JavaScript files for potentially unused imports
 */

const fs = require('fs')
const path = require('path')

function findUnusedImports(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8')
    const lines = content.split('\n')
    
    const imports = []
    const usages = new Set()
    
    // Extract imports
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim()
      
      // Skip if we're past the import section (rough heuristic)
      if (line.startsWith('export') || line.startsWith('function') || line.startsWith('const') || line.startsWith('class')) {
        break
      }
      
      // Match various import patterns
      const importMatches = [
        // import { A, B } from 'module'
        /import\s*{\s*([^}]+)\s*}\s*from/,
        // import A from 'module'
        /import\s+([A-Za-z_$][A-Za-z0-9_$]*)\s+from/,
        // import * as A from 'module'
        /import\s*\*\s*as\s+([A-Za-z_$][A-Za-z0-9_$]*)\s+from/
      ]
      
      for (const regex of importMatches) {
        const match = line.match(regex)
        if (match) {
          if (regex.source.includes('{')) {
            // Named imports
            const namedImports = match[1].split(',').map(imp => imp.trim())
            imports.push(...namedImports)
          } else {
            // Default or namespace imports
            imports.push(match[1])
          }
        }
      }
    }
    
    // Find usages in the rest of the file
    const restOfFile = lines.slice(imports.length).join('\n')
    
    for (const importName of imports) {
      // Simple usage detection (not perfect but catches most cases)
      const usagePatterns = [
        new RegExp(`\\b${importName}\\b`, 'g'),
        new RegExp(`<${importName}`, 'g'), // JSX components
        new RegExp(`${importName}\\.`, 'g'), // Method calls
        new RegExp(`${importName}\\(`, 'g'), // Function calls
      ]
      
      for (const pattern of usagePatterns) {
        if (pattern.test(restOfFile)) {
          usages.add(importName)
          break
        }
      }
    }
    
    // Find potentially unused imports
    const unused = imports.filter(imp => !usages.has(imp))
    
    return {
      file: filePath,
      totalImports: imports.length,
      usedImports: usages.size,
      unusedImports: unused,
      allImports: imports
    }
  } catch (error) {
    return {
      file: filePath,
      error: error.message
    }
  }
}

function scanDirectory(dirPath, extensions = ['.ts', '.tsx', '.js', '.jsx']) {
  const results = []
  
  function walkDirectory(currentPath) {
    try {
      const items = fs.readdirSync(currentPath)
      
      for (const item of items) {
        const itemPath = path.join(currentPath, item)
        const stat = fs.statSync(itemPath)
        
        if (stat.isDirectory()) {
          // Skip node_modules, .next, and other build directories
          if (!['node_modules', '.next', 'dist', 'build', '.git'].includes(item)) {
            walkDirectory(itemPath)
          }
        } else if (stat.isFile()) {
          const ext = path.extname(item)
          if (extensions.includes(ext)) {
            const result = findUnusedImports(itemPath)
            if (result.unusedImports && result.unusedImports.length > 0) {
              results.push(result)
            }
          }
        }
      }
    } catch (error) {
      console.error(`Error scanning ${currentPath}:`, error.message)
    }
  }
  
  walkDirectory(dirPath)
  return results
}

function generateReport() {
  console.log('🔍 Scanning for unused imports...\n')
  
  const projectRoot = process.cwd()
  const results = scanDirectory(projectRoot)
  
  if (results.length === 0) {
    console.log('✅ No unused imports found!')
    return
  }
  
  console.log(`📊 Found ${results.length} files with potentially unused imports:\n`)
  
  let totalUnused = 0
  
  for (const result of results) {
    if (result.error) {
      console.log(`❌ Error in ${result.file}: ${result.error}`)
      continue
    }
    
    const relativePath = path.relative(projectRoot, result.file)
    console.log(`📄 ${relativePath}`)
    console.log(`   Total imports: ${result.totalImports}`)
    console.log(`   Used imports: ${result.usedImports}`)
    console.log(`   Unused imports: ${result.unusedImports.length}`)
    
    if (result.unusedImports.length > 0) {
      console.log(`   🔸 Potentially unused: ${result.unusedImports.join(', ')}`)
      totalUnused += result.unusedImports.length
    }
    
    console.log('')
  }
  
  console.log(`📈 Summary:`)
  console.log(`   Files with unused imports: ${results.length}`)
  console.log(`   Total potentially unused imports: ${totalUnused}`)
  console.log('')
  console.log('⚠️  Note: This is a simple analysis. Some imports might be used in:')
  console.log('   - Type annotations')
  console.log('   - Dynamic imports')
  console.log('   - Template literals')
  console.log('   - Comments or JSDoc')
  console.log('   Please verify before removing!')
}

// Main execution
if (require.main === module) {
  generateReport()
}

module.exports = { findUnusedImports, scanDirectory }
