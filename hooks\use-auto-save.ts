import { useEffect, useRef, useCallback, useState } from 'react'

// Simple debounce utility
function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): T & { cancel: () => void } {
  let timeoutId: NodeJS.Timeout | null = null

  const debouncedFunction = ((...args: Parameters<T>) => {
    if (timeoutId) {
      clearTimeout(timeoutId)
    }

    timeoutId = setTimeout(() => {
      func(...args)
    }, delay)
  }) as T & { cancel: () => void }

  debouncedFunction.cancel = () => {
    if (timeoutId) {
      clearTimeout(timeoutId)
      timeoutId = null
    }
  }

  return debouncedFunction
}

interface UseAutoSaveOptions {
  delay?: number
  enabled?: boolean
  onSave?: (data: any) => Promise<void> | void
  onError?: (error: Error) => void
}

export function useAutoSave<T>(
  data: T,
  options: UseAutoSaveOptions = {}
) {
  const {
    delay = 2000,
    enabled = true,
    onSave,
    onError
  } = options

  const previousDataRef = useRef<T>(data)
  const saveTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  const debouncedSave = useCallback(
    debounce(async (dataToSave: T) => {
      if (!onSave || !enabled) return

      try {
        await onSave(dataToSave)
      } catch (error) {
        if (onError) {
          onError(error instanceof Error ? error : new Error('Auto-save failed'))
        }
      }
    }, delay),
    [onSave, onError, enabled, delay]
  )

  useEffect(() => {
    // Only save if data has actually changed
    if (
      enabled &&
      onSave &&
      JSON.stringify(data) !== JSON.stringify(previousDataRef.current)
    ) {
      debouncedSave(data)
      previousDataRef.current = data
    }
  }, [data, enabled, onSave, debouncedSave])

  useEffect(() => {
    return () => {
      debouncedSave.cancel()
      if (saveTimeoutRef.current) {
        clearTimeout(saveTimeoutRef.current)
      }
    }
  }, [debouncedSave])

  const saveNow = useCallback(async () => {
    if (!onSave || !enabled) return

    debouncedSave.cancel()
    
    try {
      await onSave(data)
    } catch (error) {
      if (onError) {
        onError(error instanceof Error ? error : new Error('Manual save failed'))
      }
    }
  }, [data, onSave, onError, enabled, debouncedSave])

  return { saveNow }
}

// Hook for form draft management
export function useFormDraft<T extends Record<string, any>>(
  formId: string,
  initialData: T,
  options: {
    enabled?: boolean
    clearOnSubmit?: boolean
  } = {}
) {
  const { enabled = true, clearOnSubmit = true } = options
  const storageKey = `form-draft-${formId}`

  // Load draft from localStorage on mount
  const loadDraft = useCallback((): T => {
    if (!enabled || typeof window === 'undefined') return initialData

    try {
      const saved = localStorage.getItem(storageKey)
      if (saved) {
        const parsed = JSON.parse(saved)
        // Merge with initial data to ensure all required fields exist
        return { ...initialData, ...parsed }
      }
    } catch (error) {
      console.warn('Failed to load form draft:', error)
    }
    
    return initialData
  }, [storageKey, initialData, enabled])

  // Save draft to localStorage
  const saveDraft = useCallback((data: T) => {
    if (!enabled || typeof window === 'undefined') return

    try {
      // Only save non-empty values
      const dataToSave = Object.entries(data).reduce((acc, [key, value]) => {
        if (value !== '' && value !== null && value !== undefined) {
          acc[key] = value
        }
        return acc
      }, {} as Record<string, any>)

      if (Object.keys(dataToSave).length > 0) {
        localStorage.setItem(storageKey, JSON.stringify(dataToSave))
      } else {
        localStorage.removeItem(storageKey)
      }
    } catch (error) {
      console.warn('Failed to save form draft:', error)
    }
  }, [storageKey, enabled])

  // Clear draft from localStorage
  const clearDraft = useCallback(() => {
    if (!enabled || typeof window === 'undefined') return

    try {
      localStorage.removeItem(storageKey)
    } catch (error) {
      console.warn('Failed to clear form draft:', error)
    }
  }, [storageKey, enabled])

  // Check if draft exists
  const hasDraft = useCallback((): boolean => {
    if (!enabled || typeof window === 'undefined') return false

    try {
      const saved = localStorage.getItem(storageKey)
      return saved !== null && saved !== ''
    } catch (error) {
      return false
    }
  }, [storageKey, enabled])

  return {
    loadDraft,
    saveDraft,
    clearDraft,
    hasDraft
  }
}

// Hook for managing loading states with better UX
export function useLoadingState(initialState = false) {
  const [isLoading, setIsLoading] = useState(initialState)
  const [loadingMessage, setLoadingMessage] = useState<string>('')
  const [progress, setProgress] = useState<number>(0)

  const startLoading = useCallback((message?: string) => {
    setIsLoading(true)
    setLoadingMessage(message || '')
    setProgress(0)
  }, [])

  const updateProgress = useCallback((newProgress: number, message?: string) => {
    setProgress(Math.max(0, Math.min(100, newProgress)))
    if (message) {
      setLoadingMessage(message)
    }
  }, [])

  const stopLoading = useCallback(() => {
    setIsLoading(false)
    setLoadingMessage('')
    setProgress(0)
  }, [])

  const withLoading = useCallback(async <T>(
    asyncFn: () => Promise<T>,
    message?: string
  ): Promise<T> => {
    startLoading(message)
    try {
      const result = await asyncFn()
      return result
    } finally {
      stopLoading()
    }
  }, [startLoading, stopLoading])

  return {
    isLoading,
    loadingMessage,
    progress,
    startLoading,
    updateProgress,
    stopLoading,
    withLoading
  }
}


