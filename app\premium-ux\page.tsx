'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { EnhancedButton } from '@/components/ui/enhanced-button'
import { EnhancedInput } from '@/components/ui/enhanced-input'
import { EnhancedBreadcrumb } from '@/components/navigation/enhanced-breadcrumb'
import { SmartSearch } from '@/components/search/smart-search'
import { ContextualHelp, HelpTrigger, FeatureIntro } from '@/components/help/contextual-help'
import { 
  ErrorState, 
  EmptyState, 
  LoadingState, 
  OfflineState, 
  SuccessState,
  TimeoutState 
} from '@/components/states/enhanced-states'
import { 
  MultiStepProgress, 
  LinearProgress, 
  CircularProgress,
  BreadcrumbProgress,
  LoadingProgress
} from '@/components/progress/progress-indicators'
import { 
  Sparkles, 
  Zap, 
  Heart, 
  Star,
  Play,
  Pause,
  RotateCcw,
  Settings,
  HelpCircle,
  Search,
  Bell,
  User
} from 'lucide-react'

export default function PremiumUXShowcase() {
  const [currentDemo, setCurrentDemo] = useState('interactions')
  const [showHelp, setShowHelp] = useState(false)
  const [showFeatureIntro, setShowFeatureIntro] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [progress, setProgress] = useState(0)
  const [currentStep, setCurrentStep] = useState(0)
  const [showSuccess, setShowSuccess] = useState(false)
  
  // Demo progress simulation
  useEffect(() => {
    if (isLoading) {
      const interval = setInterval(() => {
        setProgress(prev => {
          if (prev >= 100) {
            setIsLoading(false)
            setShowSuccess(true)
            setTimeout(() => setShowSuccess(false), 3000)
            return 0
          }
          return prev + 10
        })
      }, 200)
      
      return () => clearInterval(interval)
    }
  }, [isLoading])
  
  const helpSteps = [
    {
      id: '1',
      title: 'Welcome to Premium UX',
      content: 'This showcase demonstrates advanced interaction patterns and user experience enhancements.',
      target: '.demo-header'
    },
    {
      id: '2',
      title: 'Interactive Elements',
      content: 'Notice the smooth hover effects, focus states, and micro-interactions on all buttons and cards.',
      target: '.demo-buttons'
    },
    {
      id: '3',
      title: 'Smart Navigation',
      content: 'The breadcrumb and search components provide intelligent navigation with autocomplete.',
      target: '.demo-navigation'
    }
  ]
  
  const multiStepData = [
    { id: '1', title: 'Account Setup', status: 'completed' as const },
    { id: '2', title: 'Profile Information', status: 'completed' as const },
    { id: '3', title: 'Preferences', status: 'current' as const },
    { id: '4', title: 'Verification', status: 'pending' as const }
  ]
  
  const loadingSteps = [
    'Initializing account...',
    'Loading preferences...',
    'Syncing data...',
    'Finalizing setup...'
  ]
  
  return (
    <div className="min-h-screen bg-background">
      {/* Import premium styles */}
      <style jsx global>{`
        @import url('/app/premium-interactions.css');
      `}</style>
      
      <div className="max-w-7xl mx-auto p-6 space-y-8">
        {/* Header */}
        <div className="demo-header text-center space-y-4">
          <h1 className="text-display-large text-text-primary">
            Premium User Experience
            <Sparkles className="inline-block w-12 h-12 text-primary ml-4" />
          </h1>
          <p className="text-body-large text-text-secondary max-w-3xl mx-auto">
            Interactive prototype showcasing advanced micro-interactions, navigation patterns, 
            accessibility features, and comprehensive error handling.
          </p>
          
          <div className="flex items-center justify-center gap-4">
            <HelpTrigger onClick={() => setShowHelp(true)} />
            <EnhancedButton 
              variant="primary" 
              onClick={() => setShowFeatureIntro(true)}
              leftIcon={<Play className="w-4 h-4" />}
            >
              Take Tour
            </EnhancedButton>
          </div>
        </div>
        
        {/* Navigation Demo */}
        <Card className="card-base demo-navigation">
          <CardHeader className="card-header">
            <CardTitle className="flex items-center gap-3">
              <Search className="icon icon-lg text-primary" />
              Smart Navigation & Search
            </CardTitle>
          </CardHeader>
          <CardContent className="card-content space-y-6">
            <EnhancedBreadcrumb 
              items={[
                { label: 'Dashboard', href: '/dashboard' },
                { label: 'Premium UX', href: '/premium-ux' },
                { label: 'Showcase', href: '/premium-ux/showcase' }
              ]}
            />
            
            <SmartSearch 
              placeholder="Try searching for 'transactions', 'goals', or 'settings'..."
              onSearch={(query) => console.log('Search:', query)}
              onSelect={(result) => console.log('Selected:', result)}
            />
          </CardContent>
        </Card>
        
        {/* Interactive Elements Demo */}
        <Card className="card-base card-premium">
          <CardHeader className="card-header">
            <CardTitle className="flex items-center gap-3">
              <Zap className="icon icon-lg text-primary" />
              Micro-Interactions & Animations
            </CardTitle>
          </CardHeader>
          <CardContent className="card-content">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              
              {/* Premium Buttons */}
              <div className="space-y-4 demo-buttons">
                <h3 className="text-heading-medium">Enhanced Buttons</h3>
                <div className="space-y-3">
                  <EnhancedButton 
                    variant="primary" 
                    className="btn-premium w-full"
                    leftIcon={<Heart className="w-4 h-4" />}
                  >
                    Primary Action
                  </EnhancedButton>
                  
                  <EnhancedButton 
                    variant="secondary" 
                    className="btn-premium w-full"
                    loading={isLoading}
                    loadingText="Processing..."
                    onClick={() => setIsLoading(true)}
                  >
                    {isLoading ? 'Loading...' : 'Start Process'}
                  </EnhancedButton>
                  
                  <EnhancedButton 
                    variant="success" 
                    className="btn-premium w-full"
                    rightIcon={<Star className="w-4 h-4" />}
                  >
                    Success Action
                  </EnhancedButton>
                </div>
              </div>
              
              {/* Enhanced Inputs */}
              <div className="space-y-4">
                <h3 className="text-heading-medium">Smart Form Inputs</h3>
                <div className="space-y-3">
                  <EnhancedInput
                    label="Email Address"
                    placeholder="Enter your email"
                    success="Valid email format"
                    leftIcon={<User className="w-4 h-4" />}
                  />
                  
                  <EnhancedInput
                    label="Password"
                    type="password"
                    placeholder="Enter password"
                    showPasswordToggle
                    hint="Must be at least 8 characters"
                  />
                  
                  <EnhancedInput
                    label="Amount"
                    placeholder="0.00"
                    error="Amount must be greater than 0"
                    rightIcon={<span className="text-text-tertiary">$</span>}
                  />
                </div>
              </div>
              
              {/* Interactive Cards */}
              <div className="space-y-4">
                <h3 className="text-heading-medium">Interactive Cards</h3>
                <div className="space-y-3">
                  <Card className="card-premium p-4 cursor-pointer">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                        <Bell className="w-5 h-5 text-primary" />
                      </div>
                      <div>
                        <div className="font-medium">Notifications</div>
                        <div className="text-sm text-text-secondary">3 new alerts</div>
                      </div>
                    </div>
                  </Card>
                  
                  <Card className="card-premium p-4 cursor-pointer">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-success/10 rounded-lg flex items-center justify-center">
                        <Settings className="w-5 h-5 text-success" />
                      </div>
                      <div>
                        <div className="font-medium">Settings</div>
                        <div className="text-sm text-text-secondary">Customize your experience</div>
                      </div>
                    </div>
                  </Card>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
        
        {/* Progress Indicators */}
        <Card className="card-base">
          <CardHeader className="card-header">
            <CardTitle className="flex items-center gap-3">
              <RotateCcw className="icon icon-lg text-primary" />
              Progress Indicators
            </CardTitle>
          </CardHeader>
          <CardContent className="card-content space-y-8">
            
            {/* Multi-step Progress */}
            <div className="space-y-4">
              <h3 className="text-heading-medium">Multi-step Process</h3>
              <MultiStepProgress 
                steps={multiStepData}
                currentStep={2}
                showDescriptions
              />
            </div>
            
            {/* Linear Progress */}
            <div className="space-y-4">
              <h3 className="text-heading-medium">Linear Progress</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <LinearProgress 
                  value={progress} 
                  showLabel 
                  label="Upload Progress"
                />
                <LinearProgress 
                  value={75} 
                  showLabel 
                  label="Goal Achievement"
                  variant="success"
                />
              </div>
            </div>
            
            {/* Circular Progress */}
            <div className="space-y-4">
              <h3 className="text-heading-medium">Circular Progress</h3>
              <div className="flex gap-8 justify-center">
                <CircularProgress 
                  value={progress} 
                  label="Processing"
                  size={100}
                />
                <CircularProgress 
                  value={85} 
                  label="Savings Goal"
                  variant="success"
                  size={100}
                />
              </div>
            </div>
            
            {/* Loading Progress */}
            {isLoading && (
              <div className="space-y-4">
                <h3 className="text-heading-medium">Loading Steps</h3>
                <LoadingProgress 
                  steps={loadingSteps}
                  currentStep={Math.floor(progress / 25)}
                />
              </div>
            )}
          </CardContent>
        </Card>
        
        {/* State Management Demo */}
        <Card className="card-base">
          <CardHeader className="card-header">
            <CardTitle className="flex items-center gap-3">
              <HelpCircle className="icon icon-lg text-primary" />
              State Management & Error Handling
            </CardTitle>
          </CardHeader>
          <CardContent className="card-content">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              
              {/* State Buttons */}
              <div className="space-y-3">
                <EnhancedButton 
                  variant="secondary" 
                  size="sm" 
                  className="w-full"
                  onClick={() => setCurrentDemo('loading')}
                >
                  Show Loading
                </EnhancedButton>
                <EnhancedButton 
                  variant="secondary" 
                  size="sm" 
                  className="w-full"
                  onClick={() => setCurrentDemo('error')}
                >
                  Show Error
                </EnhancedButton>
                <EnhancedButton 
                  variant="secondary" 
                  size="sm" 
                  className="w-full"
                  onClick={() => setCurrentDemo('empty')}
                >
                  Show Empty State
                </EnhancedButton>
                <EnhancedButton 
                  variant="secondary" 
                  size="sm" 
                  className="w-full"
                  onClick={() => setCurrentDemo('offline')}
                >
                  Show Offline
                </EnhancedButton>
                <EnhancedButton 
                  variant="secondary" 
                  size="sm" 
                  className="w-full"
                  onClick={() => setCurrentDemo('timeout')}
                >
                  Show Timeout
                </EnhancedButton>
              </div>
              
              {/* State Display */}
              <div className="md:col-span-2 min-h-96 border border-border-default rounded-lg p-6">
                {currentDemo === 'loading' && (
                  <LoadingState 
                    title="Loading your data..."
                    description="Please wait while we fetch your information"
                  />
                )}
                
                {currentDemo === 'error' && (
                  <ErrorState 
                    variant="network"
                    onRetry={() => setCurrentDemo('interactions')}
                    onGoBack={() => setCurrentDemo('interactions')}
                  />
                )}
                
                {currentDemo === 'empty' && (
                  <EmptyState 
                    title="No transactions yet"
                    description="Start by adding your first transaction to see insights and analytics."
                    variant="transactions"
                    action={{
                      label: 'Add Transaction',
                      onClick: () => console.log('Add transaction')
                    }}
                    secondaryAction={{
                      label: 'Import Data',
                      onClick: () => console.log('Import data')
                    }}
                  />
                )}
                
                {currentDemo === 'offline' && (
                  <OfflineState 
                    onRetry={() => setCurrentDemo('interactions')}
                  />
                )}
                
                {currentDemo === 'timeout' && (
                  <TimeoutState 
                    onRetry={() => setCurrentDemo('interactions')}
                    onCancel={() => setCurrentDemo('interactions')}
                  />
                )}
                
                {currentDemo === 'interactions' && (
                  <div className="text-center py-12">
                    <Sparkles className="w-16 h-16 text-primary mx-auto mb-4" />
                    <h3 className="text-heading-medium text-text-primary mb-2">
                      Interactive Demo Ready
                    </h3>
                    <p className="text-body-medium text-text-secondary">
                      Click the buttons on the left to see different states in action.
                    </p>
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
      
      {/* Contextual Help */}
      <ContextualHelp 
        steps={helpSteps}
        isVisible={showHelp}
        onClose={() => setShowHelp(false)}
        onComplete={() => console.log('Help tour completed')}
      />
      
      {/* Feature Introduction */}
      <FeatureIntro 
        title="Premium UX Features"
        description="Discover advanced interaction patterns and user experience enhancements."
        features={[
          'Smooth micro-interactions and animations',
          'Smart navigation with autocomplete search',
          'Comprehensive error handling and recovery',
          'Accessible design with keyboard navigation',
          'Progressive loading and state management'
        ]}
        onGetStarted={() => {
          setShowFeatureIntro(false)
          setShowHelp(true)
        }}
        onSkip={() => setShowFeatureIntro(false)}
        isVisible={showFeatureIntro}
      />
      
      {/* Success State Overlay */}
      {showSuccess && (
        <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center">
          <SuccessState 
            title="Process Completed!"
            description="Your action was completed successfully with premium animations."
            showConfetti
            onClose={() => setShowSuccess(false)}
          />
        </div>
      )}
    </div>
  )
}
