import * as React from "react"
import { cn } from "@/lib/utils"
import { Loader2, CheckCircle, AlertCircle } from "lucide-react"
import { Progress } from "@/components/ui/progress"

interface LoadingSpinnerProps extends React.HTMLAttributes<HTMLDivElement> {
  size?: 'sm' | 'md' | 'lg'
  message?: string
}

export function LoadingSpinner({ 
  className, 
  size = 'md', 
  message,
  ...props 
}: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8'
  }

  return (
    <div 
      className={cn("flex items-center justify-center gap-2", className)}
      {...props}
    >
      <Loader2 className={cn("animate-spin", sizeClasses[size])} aria-hidden="true" />
      {message && (
        <span className="text-sm text-muted-foreground" aria-live="polite">
          {message}
        </span>
      )}
    </div>
  )
}

interface ProgressLoadingProps extends React.HTMLAttributes<HTMLDivElement> {
  progress: number
  message?: string
  showPercentage?: boolean
}

export function ProgressLoading({ 
  className, 
  progress, 
  message,
  showPercentage = true,
  ...props 
}: ProgressLoadingProps) {
  return (
    <div className={cn("space-y-2", className)} {...props}>
      {message && (
        <p className="text-sm text-muted-foreground text-center" aria-live="polite">
          {message}
        </p>
      )}
      <Progress 
        value={progress} 
        className="w-full"
        aria-label={`Loading progress: ${Math.round(progress)}%`}
      />
      {showPercentage && (
        <p className="text-xs text-muted-foreground text-center">
          {Math.round(progress)}%
        </p>
      )}
    </div>
  )
}

interface SkeletonProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'text' | 'circular' | 'rectangular'
  width?: string | number
  height?: string | number
  lines?: number
}

export function Skeleton({ 
  className, 
  variant = 'rectangular',
  width,
  height,
  lines = 1,
  ...props 
}: SkeletonProps) {
  const baseClasses = "animate-pulse bg-muted rounded"
  
  const variantClasses = {
    text: "h-4",
    circular: "rounded-full",
    rectangular: "rounded-md"
  }

  const style = {
    width: typeof width === 'number' ? `${width}px` : width,
    height: typeof height === 'number' ? `${height}px` : height
  }

  if (variant === 'text' && lines > 1) {
    return (
      <div className={cn("space-y-2", className)} {...props}>
        {Array.from({ length: lines }).map((_, index) => (
          <div
            key={index}
            className={cn(baseClasses, variantClasses.text)}
            style={{
              ...style,
              width: index === lines - 1 ? '75%' : '100%'
            }}
          />
        ))}
      </div>
    )
  }

  return (
    <div
      className={cn(baseClasses, variantClasses[variant], className)}
      style={style}
      {...props}
    />
  )
}

interface LoadingStateProps {
  isLoading: boolean
  error?: string | null
  success?: string | null
  children: React.ReactNode
  loadingComponent?: React.ReactNode
  errorComponent?: React.ReactNode
  successComponent?: React.ReactNode
}

export function LoadingState({
  isLoading,
  error,
  success,
  children,
  loadingComponent,
  errorComponent,
  successComponent
}: LoadingStateProps) {
  if (isLoading) {
    return (
      <div role="status" aria-live="polite">
        {loadingComponent || <LoadingSpinner message="Loading..." />}
      </div>
    )
  }

  if (error) {
    return (
      <div role="alert" aria-live="assertive">
        {errorComponent || (
          <div className="flex items-center gap-2 text-destructive">
            <AlertCircle className="h-4 w-4" aria-hidden="true" />
            <span className="text-sm">{error}</span>
          </div>
        )}
      </div>
    )
  }

  if (success) {
    return (
      <div role="status" aria-live="polite">
        {successComponent || (
          <div className="flex items-center gap-2 text-green-600">
            <CheckCircle className="h-4 w-4" aria-hidden="true" />
            <span className="text-sm">{success}</span>
          </div>
        )}
      </div>
    )
  }

  return <>{children}</>
}

interface ButtonLoadingStateProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  isLoading: boolean
  loadingText?: string
  children: React.ReactNode
}

export function ButtonLoadingState({
  isLoading,
  loadingText = "Loading...",
  disabled,
  children,
  className,
  ...props
}: ButtonLoadingStateProps) {
  return (
    <button
      className={className}
      disabled={disabled || isLoading}
      {...props}
    >
      {isLoading ? (
        <>
          <Loader2 className="mr-2 h-4 w-4 animate-spin" aria-hidden="true" />
          {loadingText}
        </>
      ) : (
        children
      )}
    </button>
  )
}

// Skeleton components for common UI patterns
export function CardSkeleton({ className, ...props }: React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div className={cn("p-6 space-y-4", className)} {...props}>
      <Skeleton variant="text" width="60%" />
      <Skeleton variant="text" lines={3} />
      <div className="flex gap-2">
        <Skeleton variant="rectangular" width={80} height={32} />
        <Skeleton variant="rectangular" width={80} height={32} />
      </div>
    </div>
  )
}

export function TableSkeleton({ 
  rows = 5, 
  columns = 4,
  className,
  ...props 
}: { 
  rows?: number
  columns?: number 
} & React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div className={cn("space-y-3", className)} {...props}>
      {/* Header */}
      <div className="flex gap-4">
        {Array.from({ length: columns }).map((_, index) => (
          <Skeleton key={`header-${index}`} variant="text" width="100%" />
        ))}
      </div>
      
      {/* Rows */}
      {Array.from({ length: rows }).map((_, rowIndex) => (
        <div key={`row-${rowIndex}`} className="flex gap-4">
          {Array.from({ length: columns }).map((_, colIndex) => (
            <Skeleton key={`cell-${rowIndex}-${colIndex}`} variant="text" width="100%" />
          ))}
        </div>
      ))}
    </div>
  )
}

export function FormSkeleton({ className, ...props }: React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div className={cn("space-y-6", className)} {...props}>
      {Array.from({ length: 4 }).map((_, index) => (
        <div key={index} className="space-y-2">
          <Skeleton variant="text" width="25%" />
          <Skeleton variant="rectangular" height={40} />
        </div>
      ))}
      <Skeleton variant="rectangular" width="100%" height={44} />
    </div>
  )
}
