'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { useAuth } from '@/lib/contexts/auth-context'
import { AuthGuard } from '@/lib/middleware/auth-guard'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2 } from 'lucide-react'
import {
  AccessibleInput,
  AccessiblePasswordInput,
  FormErrorSummary
} from '@/components/ui/accessible-form'
import { useFormValidation, commonValidationRules } from '@/hooks/use-form-validation'
import { ResponsiveContainer, MobileOptimizedCard } from '@/components/ui/responsive-container'
import { useFormDraft, useLoadingState } from '@/hooks/use-auto-save'
import { ButtonLoadingState, LoadingState } from '@/components/ui/loading-states'
import { SimpleThemeToggle } from '@/components/theme/theme-provider'

export default function SignUpPage() {
  const { signUp } = useAuth()
  const router = useRouter()

  // Form draft management
  const { loadDraft, saveDraft, clearDraft, hasDraft } = useFormDraft('signup', {
    fullName: '',
    email: '',
    password: '',
    confirmPassword: '',
  })

  const [formData, setFormData] = useState(() => loadDraft())
  const [error, setError] = useState('')
  const [success, setSuccess] = useState(false)
  const [showDraftNotice, setShowDraftNotice] = useState(false)

  // Enhanced loading state
  const {
    isLoading,
    loadingMessage,
    startLoading,
    stopLoading,
    withLoading
  } = useLoadingState()

  // Check for draft on mount
  useEffect(() => {
    if (hasDraft()) {
      setShowDraftNotice(true)
    }
  }, [])

  // Form validation setup
  const validationRules = {
    fullName: commonValidationRules.fullName,
    email: commonValidationRules.email,
    password: commonValidationRules.password,
    confirmPassword: commonValidationRules.confirmPassword
  }

  const {
    errors,
    touched,
    isValid,
    validateField,
    validateForm,
    setFieldTouched,
    clearFieldError,
    resetValidation
  } = useFormValidation(validationRules)

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    const newFormData = { ...formData, [name]: value }
    setFormData(newFormData)
    setError('') // Clear general error when user starts typing

    // Auto-save draft
    saveDraft(newFormData)

    // Real-time validation for touched fields
    if (touched[name]) {
      const fieldError = validateField(name, value, newFormData)
      if (fieldError) {
        // Set error will be handled by the validation hook
      } else {
        clearFieldError(name)
      }
    }
  }

  const handleFieldBlur = (name: string, value: string) => {
    setFieldTouched(name, true)
    const fieldError = validateField(name, value, formData)
    // The validation hook will handle setting the error
  }

  // Real-time validation effect
  useEffect(() => {
    Object.keys(touched).forEach(fieldName => {
      if (touched[fieldName]) {
        const fieldError = validateField(fieldName, formData[fieldName], formData)
        // Errors are managed by the validation hook
      }
    })
  }, [formData, touched, validateField])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Validate entire form
    const formErrors = validateForm(formData)

    // Mark all fields as touched to show errors
    Object.keys(validationRules).forEach(field => {
      setFieldTouched(field, true)
    })

    if (Object.keys(formErrors).length > 0) {
      // Focus on first error field
      const firstErrorField = Object.keys(formErrors)[0]
      const element = document.getElementById(firstErrorField)
      if (element) {
        element.focus()
        element.scrollIntoView({ behavior: 'smooth', block: 'center' })
      }
      return
    }

    await withLoading(async () => {
      setError('')
      startLoading('Creating your account...')

      try {
        const { error } = await signUp(formData.email, formData.password, formData.fullName)

        if (error) {
          setError(error.message)
        } else {
          setSuccess(true)
          resetValidation()
          clearDraft() // Clear saved draft on successful submission

          // Redirect to sign in page after successful signup
          setTimeout(() => {
            router.push('/auth/signin?message=Please check your email to confirm your account')
          }, 2000)
        }
      } catch (err) {
        setError('An unexpected error occurred. Please try again.')
      }
    })
  }

  const handleClearDraft = () => {
    clearDraft()
    setFormData({
      fullName: '',
      email: '',
      password: '',
      confirmPassword: '',
    })
    setShowDraftNotice(false)
    resetValidation()
  }

  return (
    <AuthGuard requireAuth={false}>
      <div className="min-h-screen flex items-center justify-center bg-background py-4 sm:py-12">
        <ResponsiveContainer maxWidth="md" padding="md">
          <MobileOptimizedCard variant="elevated">
          <CardHeader className="space-y-1">
            <div className="flex justify-between items-start">
              <div className="flex-1">
                <CardTitle className="text-2xl font-bold text-center">Create Account</CardTitle>
                <CardDescription className="text-center">
                  Enter your information to create your Personal Finance Tracker account
                </CardDescription>
              </div>
              <SimpleThemeToggle />
            </div>
          </CardHeader>

          <form onSubmit={handleSubmit} noValidate>
            <CardContent className="space-y-4">
              {/* Draft Notice */}
              {showDraftNotice && (
                <Alert>
                  <AlertDescription className="flex items-center justify-between">
                    <span>We found a saved draft of your form.</span>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={handleClearDraft}
                      className="h-auto p-1 text-xs"
                    >
                      Clear Draft
                    </Button>
                  </AlertDescription>
                </Alert>
              )}

              {/* Form Error Summary */}
              <FormErrorSummary errors={errors} />

              <LoadingState
                isLoading={isLoading}
                error={error}
                success={success ? "Account created successfully! Please check your email to confirm your account." : null}
              >
                <div className="space-y-4">

              <AccessibleInput
                id="fullName"
                label="Full Name"
                type="text"
                value={formData.fullName}
                onChange={handleInputChange}
                onBlur={(e) => handleFieldBlur('fullName', e.target.value)}
                placeholder="Enter your full name"
                required
                disabled={isLoading}
                error={touched.fullName ? errors.fullName : ''}
                hint="Enter your first and last name"
                autoComplete="name"
              />

              <AccessibleInput
                id="email"
                label="Email Address"
                type="email"
                value={formData.email}
                onChange={handleInputChange}
                onBlur={(e) => handleFieldBlur('email', e.target.value)}
                placeholder="Enter your email address"
                required
                disabled={isLoading}
                error={touched.email ? errors.email : ''}
                hint="We'll use this to send you account confirmations"
                autoComplete="email"
              />

              <AccessiblePasswordInput
                id="password"
                label="Password"
                value={formData.password}
                onChange={handleInputChange}
                onBlur={(e) => handleFieldBlur('password', e.target.value)}
                placeholder="Create a strong password"
                required
                disabled={isLoading}
                error={touched.password ? errors.password : ''}
                hint="Must be at least 8 characters with uppercase, lowercase, number, and special character"
                showStrengthIndicator={true}
                autoComplete="new-password"
              />

              <AccessiblePasswordInput
                id="confirmPassword"
                label="Confirm Password"
                value={formData.confirmPassword}
                onChange={handleInputChange}
                onBlur={(e) => handleFieldBlur('confirmPassword', e.target.value)}
                placeholder="Confirm your password"
                required
                disabled={isLoading}
                error={touched.confirmPassword ? errors.confirmPassword : ''}
                hint="Re-enter your password to confirm"
                autoComplete="new-password"
              />
                </div>
              </LoadingState>
            </CardContent>

            <CardFooter className="flex flex-col space-y-4">
              <ButtonLoadingState
                type="submit"
                className="w-full bg-primary text-primary-foreground hover:bg-primary/90"
                isLoading={isLoading}
                loadingText={loadingMessage || "Creating Account..."}
                disabled={isLoading || !isValid}
                aria-describedby={Object.keys(errors).length > 0 ? "form-errors" : undefined}
              >
                Create Account
              </ButtonLoadingState>

              <p className="text-sm text-center text-muted-foreground">
                Already have an account?{' '}
                <Link
                  href="/auth/signin"
                  className="text-primary hover:underline focus:ring-2 focus:ring-ring focus:ring-offset-2 rounded"
                >
                  Sign in
                </Link>
              </p>
            </CardFooter>
          </form>
          </MobileOptimizedCard>
        </ResponsiveContainer>
      </div>
    </AuthGuard>
  )
}
