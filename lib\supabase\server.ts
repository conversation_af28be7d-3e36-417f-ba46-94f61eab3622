import { createServerClient } from '@supabase/ssr'
import { Database } from '@/lib/types/database'
import { getEnvVar } from '@/lib/utils/env-validation'

/**
 * Server-side Supabase client with environment validation
 * Use this for API routes and server-side operations
 */
export function createSupabaseServerClient() {
  const supabaseUrl = getEnvVar('NEXT_PUBLIC_SUPABASE_URL')
  const supabaseAnonKey = getEnvVar('NEXT_PUBLIC_SUPABASE_ANON_KEY')

  if (!supabaseUrl || !supabaseAnonKey) {
    throw new Error(
      'Missing required Supabase environment variables. ' +
      'Please ensure NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY are set in your .env.local file.'
    )
  }

  return createServerClient<Database>(supabaseUrl, supabaseAnonKey, {
    cookies: {
      get(name: string) {
        // This will be handled by the request context
        return undefined
      },
      set(name: string, value: string, options: any) {
        // This will be handled by the response context
      },
      remove(name: string, options: any) {
        // This will be handled by the response context
      },
    },
  })
}

/**
 * Get Supabase service role client for admin operations
 * Only use this for trusted server-side operations
 */
export function createSupabaseServiceClient() {
  const supabaseUrl = getEnvVar('NEXT_PUBLIC_SUPABASE_URL')
  const serviceRoleKey = getEnvVar('SUPABASE_SERVICE_ROLE_KEY')

  if (!supabaseUrl || !serviceRoleKey) {
    throw new Error(
      'Missing required Supabase service role environment variables. ' +
      'Please ensure NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are set.'
    )
  }

  return createServerClient<Database>(supabaseUrl, serviceRoleKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    },
    cookies: {
      get(name: string) {
        return undefined
      },
      set(name: string, value: string, options: any) {
        // No-op for service role client
      },
      remove(name: string, options: any) {
        // No-op for service role client
      },
    },
  })
}
