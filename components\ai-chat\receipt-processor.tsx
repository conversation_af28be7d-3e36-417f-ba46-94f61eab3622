'use client'

import { useState, useCallback } from 'react'
import { useDropzone } from 'react-dropzone'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  Upload, 
  FileImage, 
  Loader2, 
  CheckCircle, 
  XCircle, 
  Receipt,
  Camera,
  Scan,
  Plus,
  Eye
} from 'lucide-react'
import { createTransaction, getUserCategories } from '@/lib/supabase/queries'
import { cn } from '@/lib/utils'
import { User, UserProfile } from '@/lib/types/database'
import { log } from '@/lib/utils/logger'

interface ReceiptProcessorProps {
  user: User | null
  profile: UserProfile | null
}

interface ExtractedData {
  merchant: string
  amount: number
  date: string
  items: Array<{
    name: string
    price: number
    quantity?: number
  }>
  category: string
  confidence: number
}

export function ReceiptProcessor({ user, profile }: ReceiptProcessorProps) {
  const [file, setFile] = useState<File | null>(null)
  const [processing, setProcessing] = useState(false)
  const [extractedData, setExtractedData] = useState<ExtractedData | null>(null)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const [categories, setCategories] = useState<any[]>([])
  const [previewUrl, setPreviewUrl] = useState<string | null>(null)
  const [processingStage, setProcessingStage] = useState<'uploading' | 'processing' | 'extracting' | null>(null)

  // Form data for manual editing
  const [formData, setFormData] = useState({
    description: '',
    amount: '',
    category: '',
    date: '',
    notes: ''
  })

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const file = acceptedFiles[0]
    if (file) {
      setFile(file)
      setError('')
      setSuccess('')
      setExtractedData(null)
      
      // Create preview URL
      const url = URL.createObjectURL(file)
      setPreviewUrl(url)
      
      // Auto-process the file
      processReceipt(file)
    }
  }, [])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.png', '.jpg', '.jpeg', '.gif', '.bmp', '.webp'],
      'application/pdf': ['.pdf']
    },
    maxFiles: 1,
    maxSize: 10 * 1024 * 1024 // 10MB
  })

  const processReceipt = async (file: File) => {
    setProcessing(true)
    setError('')
    setProcessingStage('uploading')

    try {
      // Load categories
      if (categories.length === 0) {
        const userCategories = await getUserCategories(user.id)
        setCategories(userCategories)
      }

      const formData = new FormData()
      formData.append('receipt', file)
      formData.append('userId', user.id)

      setProcessingStage('processing')
      
      // Simulate processing stages
      await new Promise(resolve => setTimeout(resolve, 1500))
      setProcessingStage('extracting')
      
      const response = await fetch('/api/process-receipt', {
        method: 'POST',
        body: formData
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to process receipt')
      }

      // Set extracted data
      setExtractedData(result.extractedData)
      
      // Pre-fill form with extracted data
      setFormData({
        description: result.extractedData.merchant || '',
        amount: result.extractedData.amount?.toString() || '',
        category: result.extractedData.category || '',
        date: result.extractedData.date || new Date().toISOString().split('T')[0],
        notes: result.extractedData.items?.map((item: any) => `${item.name}: ${item.price}`).join(', ') || ''
      })

      setSuccess('Receipt processed successfully!')
    } catch (err) {
      log.error('Error processing receipt', err, 'RECEIPT_PROCESSOR')
      setError(err instanceof Error ? err.message : 'Failed to process receipt')
      
      // Create mock extracted data for demo purposes
      const mockData: ExtractedData = {
        merchant: file.name.replace(/\.[^/.]+$/, "").replace(/[-_]/g, ' '),
        amount: 25.99,
        date: new Date().toISOString().split('T')[0],
        items: [
          { name: 'Sample Item 1', price: 15.99 },
          { name: 'Sample Item 2', price: 10.00 }
        ],
        category: 'Food & Dining',
        confidence: 0.85
      }
      
      setExtractedData(mockData)
      setFormData({
        description: mockData.merchant,
        amount: mockData.amount.toString(),
        category: mockData.category,
        date: mockData.date,
        notes: mockData.items.map(item => `${item.name}: $${item.price}`).join(', ')
      })
      
      setError('Using demo data - OCR processing not available')
    } finally {
      setProcessing(false)
      setProcessingStage(null)
    }
  }

  const handleCreateTransaction = async () => {
    if (!user || !formData.amount || !formData.description) {
      setError('Please fill in all required fields')
      return
    }

    try {
      setProcessing(true)
      
      await createTransaction({
        user_id: user.id,
        description: formData.description,
        amount: -Math.abs(Number(formData.amount)), // Negative for expense
        category: formData.category || 'Other',
        date: formData.date,
        notes: formData.notes,
        type: 'expense'
      })

      setSuccess('Transaction created successfully!')
      
      // Reset form
      setFile(null)
      setExtractedData(null)
      setFormData({
        description: '',
        amount: '',
        category: '',
        date: '',
        notes: ''
      })
      setPreviewUrl(null)
    } catch (err) {
      log.error('Error creating transaction', err, 'RECEIPT_PROCESSOR')
      setError('Failed to create transaction')
    } finally {
      setProcessing(false)
    }
  }

  const getProcessingProgress = () => {
    switch (processingStage) {
      case 'uploading': return 25
      case 'processing': return 50
      case 'extracting': return 75
      default: return 0
    }
  }

  return (
    <div className="grid lg:grid-cols-2 gap-6">
      {/* Upload Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Receipt className="w-5 h-5" />
            Upload Receipt
          </CardTitle>
          <CardDescription>
            Upload a receipt image or PDF and let AI extract the transaction details
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Dropzone */}
          <div
            {...getRootProps()}
            className={cn(
              "border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors",
              isDragActive ? "border-primary bg-primary/5" : "border-muted-foreground/25 hover:border-primary/50"
            )}
          >
            <input {...getInputProps()} />
            <div className="space-y-4">
              {previewUrl ? (
                <div className="space-y-2">
                  <div className="relative w-32 h-32 mx-auto">
                    <img
                      src={previewUrl}
                      alt="Receipt preview"
                      className="w-full h-full object-cover rounded-lg border"
                    />
                  </div>
                  <p className="text-sm font-medium">{file?.name}</p>
                </div>
              ) : (
                <>
                  <div className="flex justify-center">
                    <div className="p-4 bg-primary/10 rounded-full">
                      <Upload className="w-8 h-8 text-primary" />
                    </div>
                  </div>
                  <div>
                    <p className="text-lg font-medium">
                      {isDragActive ? 'Drop your receipt here' : 'Upload Receipt'}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      Drag & drop or click to select • PNG, JPG, PDF up to 10MB
                    </p>
                  </div>
                </>
              )}
            </div>
          </div>

          {/* Processing Status */}
          {processing && (
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Loader2 className="w-4 h-4 animate-spin" />
                <span className="text-sm font-medium">
                  {processingStage === 'uploading' && 'Uploading receipt...'}
                  {processingStage === 'processing' && 'Processing with OCR...'}
                  {processingStage === 'extracting' && 'Extracting transaction data...'}
                  {!processingStage && 'Processing...'}
                </span>
              </div>
              <Progress value={getProcessingProgress()} className="h-2" />
            </div>
          )}

          {/* Extracted Data Preview */}
          {extractedData && (
            <div className="space-y-3 p-4 bg-muted/50 rounded-lg">
              <div className="flex items-center gap-2">
                <Scan className="w-4 h-4 text-green-600" />
                <span className="font-medium">Extracted Data</span>
                <Badge variant="secondary">
                  {Math.round(extractedData.confidence * 100)}% confidence
                </Badge>
              </div>
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div>
                  <span className="text-muted-foreground">Merchant:</span>
                  <div className="font-medium">{extractedData.merchant}</div>
                </div>
                <div>
                  <span className="text-muted-foreground">Amount:</span>
                  <div className="font-medium">${extractedData.amount}</div>
                </div>
                <div>
                  <span className="text-muted-foreground">Date:</span>
                  <div className="font-medium">{extractedData.date}</div>
                </div>
                <div>
                  <span className="text-muted-foreground">Category:</span>
                  <div className="font-medium">{extractedData.category}</div>
                </div>
              </div>
            </div>
          )}

          {/* Status Messages */}
          {error && (
            <Alert variant="destructive">
              <XCircle className="w-4 h-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {success && (
            <Alert>
              <CheckCircle className="w-4 h-4" />
              <AlertDescription>{success}</AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Transaction Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Plus className="w-5 h-5" />
            Create Transaction
          </CardTitle>
          <CardDescription>
            Review and edit the extracted data before creating the transaction
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="description">Description *</Label>
            <Input
              id="description"
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              placeholder="Transaction description"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="amount">Amount ({profile?.currency_code || 'USD'}) *</Label>
            <Input
              id="amount"
              type="number"
              step="0.01"
              value={formData.amount}
              onChange={(e) => setFormData(prev => ({ ...prev, amount: e.target.value }))}
              placeholder="0.00"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="category">Category</Label>
            <Select
              value={formData.category}
              onValueChange={(value) => setFormData(prev => ({ ...prev, category: value }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select category" />
              </SelectTrigger>
              <SelectContent>
                {categories.map((category) => (
                  <SelectItem key={category.id} value={category.name}>
                    <div className="flex items-center space-x-2">
                      <div 
                        className="w-3 h-3 rounded-full" 
                        style={{ backgroundColor: category.color }}
                      />
                      <span>{category.name}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="date">Date</Label>
            <Input
              id="date"
              type="date"
              value={formData.date}
              onChange={(e) => setFormData(prev => ({ ...prev, date: e.target.value }))}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="notes">Notes</Label>
            <Textarea
              id="notes"
              value={formData.notes}
              onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
              placeholder="Additional notes or itemized list"
              rows={3}
            />
          </div>

          <Button 
            onClick={handleCreateTransaction}
            disabled={processing || !formData.description || !formData.amount}
            className="w-full"
          >
            {processing ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Creating Transaction...
              </>
            ) : (
              <>
                <Plus className="w-4 h-4 mr-2" />
                Create Transaction
              </>
            )}
          </Button>
        </CardContent>
      </Card>
    </div>
  )
}
