'use client'

import { useState } from 'react'
import { useAuth } from '@/lib/contexts/auth-context'
import { createGoal, updateGoal } from '@/lib/supabase/queries'
import { Goal } from '@/lib/types/database'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Target, Loader2, Calendar } from 'lucide-react'
import { format, addMonths, addYears } from 'date-fns'

interface GoalFormProps {
  goal?: Goal
  onSuccess: () => void
  onCancel: () => void
}

const goalCategories = [
  { value: 'emergency_fund', label: 'Emergency Fund', icon: '🛡️' },
  { value: 'vacation', label: 'Vacation', icon: '✈️' },
  { value: 'house_down_payment', label: 'House Down Payment', icon: '🏠' },
  { value: 'car', label: 'Car Purchase', icon: '🚗' },
  { value: 'education', label: 'Education', icon: '🎓' },
  { value: 'retirement', label: 'Retirement', icon: '🏖️' },
  { value: 'debt_payoff', label: 'Debt Payoff', icon: '💳' },
  { value: 'investment', label: 'Investment', icon: '📈' },
  { value: 'wedding', label: 'Wedding', icon: '💒' },
  { value: 'other', label: 'Other', icon: '🎯' }
]

export function GoalForm({ goal, onSuccess, onCancel }: GoalFormProps) {
  const { user, profile } = useAuth()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  
  const [formData, setFormData] = useState({
    name: goal?.name || '',
    target_amount: goal?.target_amount?.toString() || '',
    current_amount: goal?.current_amount?.toString() || '0',
    target_date: goal?.target_date || format(addYears(new Date(), 1), 'yyyy-MM-dd'),
    description: goal?.description || '',
    category: goal?.category || 'other'
  })

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
    setError('')
  }

  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }))
    setError('')
  }

  const validateForm = () => {
    if (!formData.name.trim()) {
      setError('Please enter a goal name')
      return false
    }
    
    if (!formData.target_amount || isNaN(Number(formData.target_amount)) || Number(formData.target_amount) <= 0) {
      setError('Please enter a valid target amount greater than 0')
      return false
    }
    
    if (isNaN(Number(formData.current_amount)) || Number(formData.current_amount) < 0) {
      setError('Please enter a valid current amount (0 or greater)')
      return false
    }
    
    if (Number(formData.current_amount) > Number(formData.target_amount)) {
      setError('Current amount cannot be greater than target amount')
      return false
    }
    
    if (!formData.target_date) {
      setError('Please select a target date')
      return false
    }
    
    const targetDate = new Date(formData.target_date)
    const today = new Date()
    if (targetDate <= today) {
      setError('Target date must be in the future')
      return false
    }
    
    return true
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm() || !user) return

    setLoading(true)
    setError('')

    try {
      const goalData = {
        user_id: user.id,
        name: formData.name.trim(),
        target_amount: Number(formData.target_amount),
        current_amount: Number(formData.current_amount),
        target_date: formData.target_date,
        description: formData.description.trim(),
        category: formData.category
      }

      if (goal) {
        await updateGoal(goal.id, goalData)
      } else {
        await createGoal(goalData)
      }
      
      onSuccess()
    } catch (err) {
      console.error('Error saving goal:', err)

      // Provide specific error messages based on error type
      let errorMessage = 'Failed to save goal. Please try again.'

      if (err instanceof Error) {
        if (err.message.includes('network') || err.message.includes('fetch')) {
          errorMessage = 'Network error: Unable to save goal. Please check your internet connection and try again.'
        } else if (err.message.includes('duplicate') || err.message.includes('unique')) {
          errorMessage = 'A goal with this name already exists. Please choose a different name.'
        } else if (err.message.includes('permission') || err.message.includes('unauthorized')) {
          errorMessage = 'Permission denied: You may need to sign in again to save goals.'
        } else if (err.message.includes('validation') || err.message.includes('invalid')) {
          errorMessage = 'Invalid goal data: Please check that the target amount is positive and the target date is in the future.'
        } else if (err.message.includes('timeout')) {
          errorMessage = 'Request timeout: The save operation took too long. Please try again.'
        } else if (err.message.trim()) {
          errorMessage = `Goal save failed: ${err.message}`
        }
      }

      setError(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  const calculateProgress = () => {
    const current = Number(formData.current_amount) || 0
    const target = Number(formData.target_amount) || 1
    return Math.min((current / target) * 100, 100)
  }

  const calculateMonthsRemaining = () => {
    const targetDate = new Date(formData.target_date)
    const today = new Date()
    const diffTime = targetDate.getTime() - today.getTime()
    const diffMonths = Math.ceil(diffTime / (1000 * 60 * 60 * 24 * 30))
    return Math.max(diffMonths, 0)
  }

  const calculateMonthlySavingsNeeded = () => {
    const remaining = Number(formData.target_amount) - Number(formData.current_amount || 0)
    const months = calculateMonthsRemaining()
    return months > 0 ? remaining / months : 0
  }

  const selectedCategory = goalCategories.find(cat => cat.value === formData.category)

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Target className="w-5 h-5" />
          {goal ? 'Edit Goal' : 'Create New Goal'}
        </CardTitle>
        <CardDescription>
          {goal ? 'Update your financial goal details' : 'Set up a new financial goal to track your progress'}
        </CardDescription>
      </CardHeader>

      <form onSubmit={handleSubmit}>
        <CardContent className="space-y-6">
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Goal Category */}
          <div className="space-y-2">
            <Label htmlFor="category">Goal Category *</Label>
            <Select
              value={formData.category}
              onValueChange={(value) => handleSelectChange('category', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a goal category" />
              </SelectTrigger>
              <SelectContent>
                {goalCategories.map((category) => (
                  <SelectItem key={category.value} value={category.value}>
                    <div className="flex items-center space-x-2">
                      <span>{category.icon}</span>
                      <span>{category.label}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Goal Name */}
          <div className="space-y-2">
            <Label htmlFor="name">Goal Name *</Label>
            <Input
              id="name"
              name="name"
              placeholder={`e.g., ${selectedCategory?.label || 'My Financial Goal'}`}
              value={formData.name}
              onChange={handleInputChange}
            />
          </div>

          {/* Target Amount */}
          <div className="space-y-2">
            <Label htmlFor="target_amount">Target Amount ({profile?.currency_code || 'USD'}) *</Label>
            <Input
              id="target_amount"
              name="target_amount"
              type="number"
              step="0.01"
              min="0"
              placeholder="10000.00"
              value={formData.target_amount}
              onChange={handleInputChange}
              className="text-lg"
            />
          </div>

          {/* Current Amount */}
          <div className="space-y-2">
            <Label htmlFor="current_amount">Current Amount ({profile?.currency_code || 'USD'})</Label>
            <Input
              id="current_amount"
              name="current_amount"
              type="number"
              step="0.01"
              min="0"
              placeholder="0.00"
              value={formData.current_amount}
              onChange={handleInputChange}
              className="text-lg"
            />
          </div>

          {/* Target Date */}
          <div className="space-y-2">
            <Label htmlFor="target_date">Target Date *</Label>
            <Input
              id="target_date"
              name="target_date"
              type="date"
              value={formData.target_date}
              onChange={handleInputChange}
            />
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              name="description"
              placeholder="Optional description of your goal..."
              value={formData.description}
              onChange={handleInputChange}
              rows={3}
            />
          </div>

          {/* Progress Preview */}
          {formData.target_amount && Number(formData.target_amount) > 0 && (
            <div className="p-4 bg-muted rounded-lg space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Progress Preview</span>
                <span className="text-sm text-muted-foreground">
                  {calculateProgress().toFixed(1)}%
                </span>
              </div>
              
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-primary h-2 rounded-full transition-all duration-300"
                  style={{ width: `${calculateProgress()}%` }}
                />
              </div>
              
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-muted-foreground">Months remaining:</span>
                  <div className="font-medium">{calculateMonthsRemaining()}</div>
                </div>
                <div>
                  <span className="text-muted-foreground">Monthly savings needed:</span>
                  <div className="font-medium">
                    {new Intl.NumberFormat('en-US', {
                      style: 'currency',
                      currency: profile?.currency_code || 'USD',
                    }).format(calculateMonthlySavingsNeeded())}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex justify-end space-x-3 pt-4">
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {goal ? 'Update Goal' : 'Create Goal'}
            </Button>
          </div>
        </CardContent>
      </form>
    </Card>
  )
}
