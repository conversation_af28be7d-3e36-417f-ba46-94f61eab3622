import React from 'react'
import { cn } from '@/lib/utils'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { AlertCircle, Info, CheckCircle, Eye, EyeOff } from 'lucide-react'

interface AccessibleFieldProps {
  id: string
  label: string
  required?: boolean
  error?: string
  hint?: string
  success?: string
  className?: string
  children: React.ReactNode
}

export function AccessibleField({
  id,
  label,
  required = false,
  error,
  hint,
  success,
  className,
  children
}: AccessibleFieldProps) {
  const hintId = hint ? `${id}-hint` : undefined
  const errorId = error ? `${id}-error` : undefined
  const successId = success ? `${id}-success` : undefined

  const describedBy = [hintId, errorId, successId].filter(Boolean).join(' ')

  return (
    <div className={cn('space-y-2', className)}>
      <Label 
        htmlFor={id}
        className={cn(
          'text-sm font-medium',
          error ? 'text-red-700' : 'text-gray-700'
        )}
      >
        {label}
        {required && (
          <span 
            className="text-red-500 ml-1" 
            aria-label="required"
            title="This field is required"
          >
            *
          </span>
        )}
      </Label>

      <div className="relative">
        {React.cloneElement(children as React.ReactElement, {
          'aria-describedby': describedBy || undefined,
          'aria-invalid': error ? 'true' : 'false',
          'aria-required': required,
          className: cn(
            (children as React.ReactElement).props?.className || '',
            error ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : '',
            success ? 'border-green-500 focus:border-green-500 focus:ring-green-500' : ''
          )
        })}

        {/* Success icon */}
        {success && (
          <CheckCircle 
            className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-green-500"
            aria-hidden="true"
          />
        )}

        {/* Error icon */}
        {error && (
          <AlertCircle 
            className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-red-500"
            aria-hidden="true"
          />
        )}
      </div>

      {/* Hint text */}
      {hint && (
        <p 
          id={hintId}
          className="text-sm text-gray-600 flex items-start gap-1"
        >
          <Info className="w-3 h-3 mt-0.5 flex-shrink-0" aria-hidden="true" />
          {hint}
        </p>
      )}

      {/* Error message */}
      {error && (
        <p 
          id={errorId}
          className="text-sm text-red-600 flex items-start gap-1"
          role="alert"
          aria-live="polite"
        >
          <AlertCircle className="w-3 h-3 mt-0.5 flex-shrink-0" aria-hidden="true" />
          {error}
        </p>
      )}

      {/* Success message */}
      {success && (
        <p 
          id={successId}
          className="text-sm text-green-600 flex items-start gap-1"
          role="status"
          aria-live="polite"
        >
          <CheckCircle className="w-3 h-3 mt-0.5 flex-shrink-0" aria-hidden="true" />
          {success}
        </p>
      )}
    </div>
  )
}

// Accessible input field
export function AccessibleInput({
  id,
  label,
  type = 'text',
  placeholder,
  value,
  onChange,
  required = false,
  error,
  hint,
  success,
  className,
  ...props
}: {
  id: string
  label: string
  type?: string
  placeholder?: string
  value?: string
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void
  required?: boolean
  error?: string
  hint?: string
  success?: string
  className?: string
} & React.InputHTMLAttributes<HTMLInputElement>) {
  return (
    <AccessibleField
      id={id}
      label={label}
      required={required}
      error={error}
      hint={hint}
      success={success}
      className={className}
    >
      <Input
        type={type}
        placeholder={placeholder}
        value={value}
        onChange={onChange}
        {...props}
      />
    </AccessibleField>
  )
}

// Accessible textarea field
export function AccessibleTextarea({
  id,
  label,
  placeholder,
  value,
  onChange,
  required = false,
  error,
  hint,
  success,
  className,
  rows = 3,
  ...props
}: {
  id: string
  label: string
  placeholder?: string
  value?: string
  onChange?: (e: React.ChangeEvent<HTMLTextAreaElement>) => void
  required?: boolean
  error?: string
  hint?: string
  success?: string
  className?: string
  rows?: number
} & React.TextareaHTMLAttributes<HTMLTextAreaElement>) {
  return (
    <AccessibleField
      id={id}
      label={label}
      required={required}
      error={error}
      hint={hint}
      success={success}
      className={className}
    >
      <Textarea
        placeholder={placeholder}
        value={value}
        onChange={onChange}
        rows={rows}
        {...props}
      />
    </AccessibleField>
  )
}

// Accessible select field
export function AccessibleSelect({
  id,
  label,
  value,
  onValueChange,
  placeholder = 'Select an option',
  required = false,
  error,
  hint,
  success,
  className,
  children,
  ...props
}: {
  id: string
  label: string
  value?: string
  onValueChange?: (value: string) => void
  placeholder?: string
  required?: boolean
  error?: string
  hint?: string
  success?: string
  className?: string
  children: React.ReactNode
}) {
  return (
    <AccessibleField
      id={id}
      label={label}
      required={required}
      error={error}
      hint={hint}
      success={success}
      className={className}
    >
      <Select value={value} onValueChange={onValueChange} {...props}>
        <SelectTrigger>
          <SelectValue placeholder={placeholder} />
        </SelectTrigger>
        <SelectContent>
          {children}
        </SelectContent>
      </Select>
    </AccessibleField>
  )
}

// Accessible password input with strength indicator
export function AccessiblePasswordInput({
  id,
  label,
  value,
  onChange,
  required = false,
  error,
  hint,
  success,
  className,
  showStrengthIndicator = false,
  ...props
}: {
  id: string
  label: string
  value?: string
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void
  required?: boolean
  error?: string
  hint?: string
  success?: string
  className?: string
  showStrengthIndicator?: boolean
} & React.InputHTMLAttributes<HTMLInputElement>) {
  const [showPassword, setShowPassword] = React.useState(false)

  // Calculate password strength
  const calculateStrength = (password: string): { score: number; text: string } => {
    if (!password) return { score: 0, text: '' }

    let score = 0
    const checks = [
      password.length >= 8,
      /[a-z]/.test(password),
      /[A-Z]/.test(password),
      /\d/.test(password),
      /[^a-zA-Z\d]/.test(password)
    ]

    score = checks.filter(Boolean).length

    const strengthTexts = ['', 'Very weak', 'Weak', 'Fair', 'Good', 'Strong']
    return { score, text: strengthTexts[score] }
  }

  const strength = showStrengthIndicator ? calculateStrength(value || '') : { score: 0, text: '' }

  const strengthColors = [
    'bg-gray-200', // 0: No password
    'bg-red-500',  // 1: Very weak
    'bg-orange-500', // 2: Weak
    'bg-yellow-500', // 3: Fair
    'bg-blue-500',   // 4: Good
    'bg-green-500'   // 5: Strong
  ]

  return (
    <AccessibleField
      id={id}
      label={label}
      required={required}
      error={error}
      hint={hint}
      success={success}
      className={className}
    >
      <div className="space-y-2">
        <div className="relative">
          <Input
            type={showPassword ? 'text' : 'password'}
            value={value}
            onChange={onChange}
            className="pr-14 sm:pr-12"
            {...props}
          />
          <Button
            type="button"
            variant="ghost"
            size="icon-sm"
            className="absolute right-1 top-1/2 -translate-y-1/2 h-8 w-8 hover:bg-transparent focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 min-h-[44px] min-w-[44px] sm:min-h-[32px] sm:min-w-[32px]"
            onClick={() => setShowPassword(!showPassword)}
            aria-label={showPassword ? "Hide password" : "Show password"}
            aria-pressed={showPassword}
            tabIndex={0}
          >
            {showPassword ? (
              <EyeOff className="h-5 w-5" aria-hidden="true" />
            ) : (
              <Eye className="h-5 w-5" aria-hidden="true" />
            )}
          </Button>
        </div>

        {/* Password Strength Indicator */}
        {showStrengthIndicator && value && (
          <div className="space-y-1" role="progressbar" aria-valuenow={strength.score} aria-valuemin={0} aria-valuemax={5} aria-label="Password strength">
            <div className="flex space-x-1">
              {[1, 2, 3, 4, 5].map((level) => (
                <div
                  key={level}
                  className={cn(
                    "h-2 flex-1 rounded-full transition-colors",
                    level <= strength.score
                      ? strengthColors[strength.score]
                      : "bg-gray-200"
                  )}
                />
              ))}
            </div>
            {strength.text && (
              <p className="text-xs text-muted-foreground" aria-live="polite">
                Password strength: {strength.text}
              </p>
            )}
          </div>
        )}
      </div>
    </AccessibleField>
  )
}

// Accessible checkbox field
export function AccessibleCheckbox({
  id,
  label,
  checked,
  onCheckedChange,
  required = false,
  error,
  hint,
  success,
  className,
  ...props
}: {
  id: string
  label: string
  checked?: boolean
  onCheckedChange?: (checked: boolean) => void
  required?: boolean
  error?: string
  hint?: string
  success?: string
  className?: string
}) {
  return (
    <AccessibleField
      id={id}
      label={label}
      required={required}
      error={error}
      hint={hint}
      success={success}
      className={className}
    >
      <div className="flex items-center space-x-2">
        <input
          type="checkbox"
          id={id}
          checked={checked}
          onChange={(e) => onCheckedChange?.(e.target.checked)}
          className={cn(
            'w-4 h-4 text-primary bg-background border-input rounded focus:ring-ring focus:ring-2',
            error ? 'border-destructive' : ''
          )}
          {...props}
        />
        <Label htmlFor={id} className="text-sm font-normal cursor-pointer">
          {label}
        </Label>
      </div>
    </AccessibleField>
  )
}

// Form error summary component
export function FormErrorSummary({
  errors,
  title = "Please fix the following errors:"
}: {
  errors: Record<string, string>
  title?: string
}) {
  const errorEntries = Object.entries(errors).filter(([, error]) => error)

  if (errorEntries.length === 0) return null

  return (
    <div
      className="rounded-md bg-destructive/10 border border-destructive/20 p-4 mb-4"
      role="alert"
      aria-live="polite"
    >
      <div className="flex">
        <AlertCircle className="h-5 w-5 text-destructive flex-shrink-0" aria-hidden="true" />
        <div className="ml-3">
          <h3 className="text-sm font-medium text-destructive">{title}</h3>
          <div className="mt-2 text-sm text-destructive">
            <ul className="list-disc list-inside space-y-1">
              {errorEntries.map(([field, error]) => (
                <li key={field}>
                  <button
                    type="button"
                    className="underline hover:no-underline focus:ring-2 focus:ring-ring focus:ring-offset-2 rounded"
                    onClick={() => {
                      const element = document.getElementById(field)
                      if (element) {
                        element.focus()
                        element.scrollIntoView({ behavior: 'smooth', block: 'center' })
                      }
                    }}
                  >
                    {error}
                  </button>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
