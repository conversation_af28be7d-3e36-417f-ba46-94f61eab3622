/* ===================================================================
   PREMIUM USER EXPERIENCE - MICRO-INTERACTIONS & ANIMATIONS
   ================================================================== */

/* ===================================================================
   1. ANIMATION FOUNDATIONS
   ================================================================== */

:root {
  /* Animation Timing */
  --duration-instant: 0.1s;
  --duration-fast: 0.15s;
  --duration-normal: 0.2s;
  --duration-slow: 0.3s;
  --duration-slower: 0.5s;
  
  /* Easing Functions */
  --ease-out-quart: cubic-bezier(0.25, 1, 0.5, 1);
  --ease-in-out-quart: cubic-bezier(0.76, 0, 0.24, 1);
  --ease-spring: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --ease-smooth: cubic-bezier(0.4, 0, 0.2, 1);
  
  /* Transform Origins */
  --transform-origin-center: center;
  --transform-origin-top: top;
  --transform-origin-bottom: bottom;
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* ===================================================================
   2. MICRO-INTERACTIONS
   ================================================================== */

/* Enhanced Button Interactions */
.btn-premium {
  position: relative;
  overflow: hidden;
  transition: all var(--duration-normal) var(--ease-smooth);
  transform: translateY(0);
  
  /* Ripple Effect Container */
  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width var(--duration-slow) var(--ease-out-quart),
                height var(--duration-slow) var(--ease-out-quart);
  }
  
  /* Hover State */
  &:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
    
    &::before {
      width: 120%;
      height: 120%;
    }
  }
  
  /* Active State */
  &:active:not(:disabled) {
    transform: translateY(0);
    transition-duration: var(--duration-fast);
  }
  
  /* Focus State */
  &:focus-visible {
    outline: 2px solid var(--primary);
    outline-offset: 2px;
    box-shadow: 0 0 0 4px var(--primary-50);
  }
}

/* Interactive Card Hover */
.card-premium {
  transition: all var(--duration-normal) var(--ease-smooth);
  cursor: pointer;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-200);
  }
  
  &:active {
    transform: translateY(-1px);
    transition-duration: var(--duration-fast);
  }
}

/* Form Input Interactions */
.input-premium {
  position: relative;
  transition: all var(--duration-normal) var(--ease-smooth);
  
  &:focus {
    transform: scale(1.01);
    box-shadow: 0 0 0 3px var(--primary-50);
  }
  
  &:focus + .input-label {
    color: var(--primary);
    transform: translateY(-8px) scale(0.85);
  }
}

/* Floating Label Animation */
.input-label {
  position: absolute;
  top: 50%;
  left: 12px;
  transform: translateY(-50%);
  transition: all var(--duration-normal) var(--ease-smooth);
  pointer-events: none;
  color: var(--text-tertiary);
  background: var(--surface);
  padding: 0 4px;
}

.input-premium:not(:placeholder-shown) + .input-label,
.input-premium:focus + .input-label {
  top: 0;
  transform: translateY(-50%) scale(0.85);
  color: var(--primary);
}

/* ===================================================================
   3. LOADING ANIMATIONS
   ================================================================== */

/* Skeleton Screen */
.skeleton {
  background: linear-gradient(
    90deg,
    var(--neutral-200) 25%,
    var(--neutral-100) 50%,
    var(--neutral-200) 75%
  );
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: var(--radius-md);
}

@keyframes skeleton-loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* Branded Spinner */
.spinner-premium {
  width: 40px;
  height: 40px;
  border: 3px solid var(--neutral-200);
  border-top: 3px solid var(--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  
  &.spinner-sm { width: 20px; height: 20px; border-width: 2px; }
  &.spinner-lg { width: 60px; height: 60px; border-width: 4px; }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Pulse Loading */
.pulse-loading {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* ===================================================================
   4. PAGE TRANSITIONS
   ================================================================== */

/* Slide Transitions */
.page-transition-enter {
  opacity: 0;
  transform: translateX(20px);
}

.page-transition-enter-active {
  opacity: 1;
  transform: translateX(0);
  transition: opacity var(--duration-slow) var(--ease-smooth),
              transform var(--duration-slow) var(--ease-smooth);
}

.page-transition-exit {
  opacity: 1;
  transform: translateX(0);
}

.page-transition-exit-active {
  opacity: 0;
  transform: translateX(-20px);
  transition: opacity var(--duration-slow) var(--ease-smooth),
              transform var(--duration-slow) var(--ease-smooth);
}

/* Fade Transitions */
.fade-enter {
  opacity: 0;
}

.fade-enter-active {
  opacity: 1;
  transition: opacity var(--duration-slow) var(--ease-smooth);
}

.fade-exit {
  opacity: 1;
}

.fade-exit-active {
  opacity: 0;
  transition: opacity var(--duration-slow) var(--ease-smooth);
}

/* ===================================================================
   5. SUCCESS ANIMATIONS
   ================================================================== */

/* Checkmark Animation */
.success-checkmark {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: var(--success);
  position: relative;
  animation: success-scale var(--duration-slow) var(--ease-spring);
  
  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 12px;
    border: 3px solid white;
    border-top: none;
    border-right: none;
    transform: translate(-50%, -60%) rotate(-45deg);
    animation: success-check var(--duration-slow) var(--ease-smooth) 0.2s both;
  }
}

@keyframes success-scale {
  0% { transform: scale(0); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

@keyframes success-check {
  0% { width: 0; height: 0; }
  100% { width: 20px; height: 12px; }
}

/* Celebration Confetti */
.confetti {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 9999;
}

.confetti-piece {
  position: absolute;
  width: 8px;
  height: 8px;
  background: var(--primary);
  animation: confetti-fall 3s linear forwards;
}

@keyframes confetti-fall {
  0% {
    transform: translateY(-100vh) rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: translateY(100vh) rotate(720deg);
    opacity: 0;
  }
}

/* ===================================================================
   6. TOOLTIP SYSTEM
   ================================================================== */

.tooltip-container {
  position: relative;
  display: inline-block;
}

.tooltip {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%) translateY(-8px);
  background: var(--neutral-900);
  color: white;
  padding: 8px 12px;
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all var(--duration-normal) var(--ease-smooth);
  z-index: 1000;
  
  /* Arrow */
  &::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-top-color: var(--neutral-900);
  }
  
  /* Show on hover/focus */
  .tooltip-container:hover &,
  .tooltip-container:focus-within & {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(-4px);
    transition-delay: var(--duration-slow);
  }
}

/* ===================================================================
   7. FOCUS INDICATORS
   ================================================================== */

/* Enhanced Focus Ring */
.focus-ring {
  &:focus-visible {
    outline: 2px solid var(--primary);
    outline-offset: 2px;
    box-shadow: 0 0 0 4px var(--primary-50);
    border-radius: var(--radius-md);
  }
}

/* Skip Link */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--primary);
  color: white;
  padding: 8px 16px;
  text-decoration: none;
  border-radius: var(--radius-md);
  z-index: 9999;
  transition: top var(--duration-normal) var(--ease-smooth);
  
  &:focus {
    top: 6px;
  }
}

/* ===================================================================
   8. RESPONSIVE TOUCH INTERACTIONS
   ================================================================== */

/* Touch Feedback */
@media (hover: none) and (pointer: coarse) {
  .btn-premium:active {
    background-color: var(--primary-600);
    transform: scale(0.98);
  }
  
  .card-premium:active {
    transform: scale(0.98);
    transition-duration: var(--duration-fast);
  }
}

/* Swipe Indicators */
.swipe-indicator {
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: 50%;
    right: 16px;
    width: 20px;
    height: 2px;
    background: var(--text-tertiary);
    transform: translateY(-50%);
    animation: swipe-hint 2s ease-in-out infinite;
  }
}

@keyframes swipe-hint {
  0%, 100% { transform: translateY(-50%) translateX(0); opacity: 0.5; }
  50% { transform: translateY(-50%) translateX(-8px); opacity: 1; }
}
