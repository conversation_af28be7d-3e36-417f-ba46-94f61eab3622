/* ===================================================================
   PERSONAL FINANCE TRACKER - PERFECTED DESIGN SYSTEM
   ================================================================== */

/* ===================================================================
   1. FOUNDATIONAL TOKENS
   ================================================================== */

:root {
  /* TYPOGRAPHY SCALE - Perfect 1.25 Ratio */
  --font-size-xs: 0.75rem;      /* 12px - Small labels, captions */
  --font-size-sm: 0.9375rem;    /* 15px - Secondary text, metadata */
  --font-size-base: 1.125rem;   /* 18px - Body text, primary content */
  --font-size-lg: 1.5rem;       /* 24px - Subheadings, card titles */
  --font-size-xl: 1.875rem;     /* 30px - Section headings */
  --font-size-2xl: 2.3125rem;   /* 37px - Page headings */
  --font-size-3xl: 2.875rem;    /* 46px - Hero headings */

  /* FONT WEIGHTS - Clear Purpose Hierarchy */
  --font-weight-light: 300;     /* Light - Large display text */
  --font-weight-regular: 400;   /* Regular - Body text, labels */
  --font-weight-semibold: 600;  /* Semibold - Emphasis, buttons */
  --font-weight-bold: 700;      /* Bold - Headings, strong emphasis */

  /* LINE HEIGHTS - Optimized for Readability */
  --line-height-heading: 1.2;   /* Tight - Headings and titles */
  --line-height-body: 1.5;      /* Normal - Body text and paragraphs */
  --line-height-ui: 1.4;        /* UI - Buttons, form elements */

  /* LETTER SPACING - Optical Refinement */
  --letter-spacing-tight: -0.02em;  /* Large text, headings */
  --letter-spacing-normal: 0;       /* Body text */
  --letter-spacing-wide: 0.05em;    /* Small caps, labels */

  /* SPACING SYSTEM - 4px/8px Base Unit */
  --space-1: 0.25rem;   /* 4px - Micro spacing */
  --space-2: 0.5rem;    /* 8px - Small spacing */
  --space-3: 0.75rem;   /* 12px - Compact spacing */
  --space-4: 1rem;      /* 16px - Standard spacing */
  --space-6: 1.5rem;    /* 24px - Medium spacing */
  --space-8: 2rem;      /* 32px - Large spacing */
  --space-12: 3rem;     /* 48px - Section spacing */
  --space-16: 4rem;     /* 64px - Layout spacing */

  /* BORDER RADIUS - Consistent Curvature */
  --radius-xs: 0.125rem;  /* 2px - Subtle rounding */
  --radius-sm: 0.25rem;   /* 4px - Small elements */
  --radius-md: 0.5rem;    /* 8px - Standard elements */
  --radius-lg: 0.75rem;   /* 12px - Cards, containers */
  --radius-xl: 1rem;      /* 16px - Large containers */

  /* COMPONENT DIMENSIONS - Standardized Sizing */
  --button-height-sm: 2rem;      /* 32px - Compact buttons */
  --button-height-md: 2.5rem;    /* 40px - Standard buttons */
  --button-height-lg: 3rem;      /* 48px - Prominent buttons */
  
  --input-height-sm: 2rem;       /* 32px - Compact inputs */
  --input-height-md: 2.5rem;     /* 40px - Standard inputs */
  --input-height-lg: 3rem;       /* 48px - Large inputs */

  --icon-size-sm: 1rem;          /* 16px - Small icons */
  --icon-size-md: 1.25rem;       /* 20px - Standard icons */
  --icon-size-lg: 1.5rem;        /* 24px - Large icons */
  --icon-size-xl: 2rem;          /* 32px - Hero icons */

  /* SHADOWS - Depth and Elevation */
  --shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
}

/* ===================================================================
   2. PERFECTED COLOR SYSTEM - WCAG AA COMPLIANT
   ================================================================== */

:root {
  /* PRIMARY PALETTE - Financial Green Theme */
  --primary-50: oklch(0.97 0.02 142);     /* Lightest tint */
  --primary-100: oklch(0.94 0.04 142);    /* Very light */
  --primary-200: oklch(0.88 0.08 142);    /* Light */
  --primary-300: oklch(0.82 0.12 142);    /* Medium light */
  --primary-400: oklch(0.76 0.16 142);    /* Medium */
  --primary-500: oklch(0.70 0.20 142);    /* Base primary */
  --primary-600: oklch(0.64 0.18 142);    /* Medium dark */
  --primary-700: oklch(0.58 0.16 142);    /* Dark */
  --primary-800: oklch(0.52 0.14 142);    /* Very dark */
  --primary-900: oklch(0.46 0.12 142);    /* Darkest */

  /* SECONDARY PALETTE - Professional Blue */
  --secondary-50: oklch(0.97 0.02 220);
  --secondary-100: oklch(0.94 0.04 220);
  --secondary-200: oklch(0.88 0.08 220);
  --secondary-300: oklch(0.82 0.12 220);
  --secondary-400: oklch(0.76 0.16 220);
  --secondary-500: oklch(0.70 0.20 220);
  --secondary-600: oklch(0.64 0.18 220);
  --secondary-700: oklch(0.58 0.16 220);
  --secondary-800: oklch(0.52 0.14 220);
  --secondary-900: oklch(0.46 0.12 220);

  /* SEMANTIC COLORS - Status Communication */
  --success-50: oklch(0.97 0.02 142);
  --success-500: oklch(0.70 0.20 142);    /* 4.5:1 contrast on white */
  --success-700: oklch(0.58 0.16 142);    /* 7:1 contrast on white */

  --warning-50: oklch(0.97 0.02 84);
  --warning-500: oklch(0.75 0.15 84);     /* 4.5:1 contrast on white */
  --warning-700: oklch(0.65 0.13 84);     /* 7:1 contrast on white */

  --error-50: oklch(0.97 0.02 27);
  --error-500: oklch(0.65 0.20 27);       /* 4.5:1 contrast on white */
  --error-700: oklch(0.55 0.18 27);       /* 7:1 contrast on white */

  --info-50: oklch(0.97 0.02 220);
  --info-500: oklch(0.65 0.15 220);       /* 4.5:1 contrast on white */
  --info-700: oklch(0.55 0.13 220);       /* 7:1 contrast on white */

  /* NEUTRAL PALETTE - Grayscale Foundation */
  --neutral-50: oklch(0.98 0.002 286);    /* Almost white */
  --neutral-100: oklch(0.96 0.002 286);   /* Very light gray */
  --neutral-200: oklch(0.92 0.004 286);   /* Light gray */
  --neutral-300: oklch(0.86 0.006 286);   /* Medium light gray */
  --neutral-400: oklch(0.74 0.008 286);   /* Medium gray */
  --neutral-500: oklch(0.62 0.010 286);   /* Base gray */
  --neutral-600: oklch(0.50 0.008 286);   /* Medium dark gray */
  --neutral-700: oklch(0.38 0.006 286);   /* Dark gray */
  --neutral-800: oklch(0.26 0.004 286);   /* Very dark gray */
  --neutral-900: oklch(0.14 0.002 286);   /* Almost black */
}

/* ===================================================================
   3. LIGHT THEME SEMANTIC TOKENS
   ================================================================== */

:root {
  /* Background Colors */
  --background: var(--neutral-50);
  --surface: var(--neutral-50);
  --surface-elevated: oklch(1 0 0);

  /* Text Colors - WCAG AA Compliant */
  --text-primary: var(--neutral-900);     /* 15.3:1 contrast */
  --text-secondary: var(--neutral-700);   /* 8.2:1 contrast */
  --text-tertiary: var(--neutral-600);    /* 5.9:1 contrast */
  --text-disabled: var(--neutral-400);    /* 3.1:1 contrast */

  /* Interactive Colors */
  --primary: var(--primary-500);
  --primary-hover: var(--primary-600);
  --primary-active: var(--primary-700);
  --primary-foreground: oklch(1 0 0);

  --secondary: var(--secondary-500);
  --secondary-hover: var(--secondary-600);
  --secondary-active: var(--secondary-700);
  --secondary-foreground: oklch(1 0 0);

  /* Border Colors */
  --border-subtle: var(--neutral-200);
  --border-default: var(--neutral-300);
  --border-strong: var(--neutral-400);

  /* Status Colors */
  --success: var(--success-500);
  --success-foreground: oklch(1 0 0);
  --warning: var(--warning-500);
  --warning-foreground: var(--neutral-900);
  --error: var(--error-500);
  --error-foreground: oklch(1 0 0);
  --info: var(--info-500);
  --info-foreground: oklch(1 0 0);
}

/* ===================================================================
   4. DARK THEME OVERRIDES
   ================================================================== */

.dark {
  /* Background Colors */
  --background: var(--neutral-900);
  --surface: var(--neutral-800);
  --surface-elevated: var(--neutral-700);

  /* Text Colors - Inverted for Dark Mode */
  --text-primary: var(--neutral-50);      /* High contrast on dark */
  --text-secondary: var(--neutral-300);   /* Medium contrast */
  --text-tertiary: var(--neutral-400);    /* Lower contrast */
  --text-disabled: var(--neutral-600);    /* Disabled state */

  /* Interactive Colors - Adjusted for Dark Mode */
  --primary: var(--primary-400);          /* Lighter for dark backgrounds */
  --primary-hover: var(--primary-300);
  --primary-active: var(--primary-200);
  --primary-foreground: var(--neutral-900);

  --secondary: var(--secondary-400);
  --secondary-hover: var(--secondary-300);
  --secondary-active: var(--secondary-200);
  --secondary-foreground: var(--neutral-900);

  /* Border Colors */
  --border-subtle: var(--neutral-700);
  --border-default: var(--neutral-600);
  --border-strong: var(--neutral-500);

  /* Status Colors - Dark Mode Variants */
  --success: var(--success-400);
  --warning: var(--warning-400);
  --error: var(--error-400);
  --info: var(--info-400);
}

/* ===================================================================
   5. TYPOGRAPHY CLASSES
   ================================================================== */

.text-display-large {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-light);
  line-height: var(--line-height-heading);
  letter-spacing: var(--letter-spacing-tight);
}

.text-display-medium {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-regular);
  line-height: var(--line-height-heading);
  letter-spacing: var(--letter-spacing-tight);
}

.text-heading-large {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-heading);
  letter-spacing: var(--letter-spacing-normal);
}

.text-heading-medium {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-heading);
  letter-spacing: var(--letter-spacing-normal);
}

.text-body-large {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-regular);
  line-height: var(--line-height-body);
  letter-spacing: var(--letter-spacing-normal);
}

.text-body-medium {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-regular);
  line-height: var(--line-height-body);
  letter-spacing: var(--letter-spacing-normal);
}

.text-label-large {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-ui);
  letter-spacing: var(--letter-spacing-wide);
}

.text-label-small {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-ui);
  letter-spacing: var(--letter-spacing-wide);
  text-transform: uppercase;
}

/* ===================================================================
   6. STANDARDIZED BUTTON SYSTEM
   ================================================================== */

.btn-base {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  border-radius: var(--radius-md);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-ui);
  letter-spacing: var(--letter-spacing-normal);
  transition: all 0.15s ease-in-out;
  cursor: pointer;
  border: 1px solid transparent;
  text-decoration: none;
  white-space: nowrap;
  user-select: none;

  /* Focus ring for accessibility */
  &:focus-visible {
    outline: 2px solid var(--primary);
    outline-offset: 2px;
  }

  /* Disabled state */
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
  }
}

/* Button Sizes */
.btn-sm {
  height: var(--button-height-sm);
  padding: 0 var(--space-3);
  font-size: var(--font-size-sm);

  .icon {
    width: var(--icon-size-sm);
    height: var(--icon-size-sm);
  }
}

.btn-md {
  height: var(--button-height-md);
  padding: 0 var(--space-4);
  font-size: var(--font-size-sm);

  .icon {
    width: var(--icon-size-md);
    height: var(--icon-size-md);
  }
}

.btn-lg {
  height: var(--button-height-lg);
  padding: 0 var(--space-6);
  font-size: var(--font-size-base);

  .icon {
    width: var(--icon-size-lg);
    height: var(--icon-size-lg);
  }
}

/* Button Variants */
.btn-primary {
  background-color: var(--primary);
  color: var(--primary-foreground);
  border-color: var(--primary);
  box-shadow: var(--shadow-sm);

  &:hover:not(:disabled) {
    background-color: var(--primary-hover);
    border-color: var(--primary-hover);
    box-shadow: var(--shadow-md);
  }

  &:active:not(:disabled) {
    background-color: var(--primary-active);
    border-color: var(--primary-active);
    box-shadow: var(--shadow-xs);
    transform: translateY(1px);
  }
}

.btn-secondary {
  background-color: transparent;
  color: var(--text-primary);
  border-color: var(--border-default);

  &:hover:not(:disabled) {
    background-color: var(--neutral-100);
    border-color: var(--border-strong);
  }

  &:active:not(:disabled) {
    background-color: var(--neutral-200);
    transform: translateY(1px);
  }
}

.btn-ghost {
  background-color: transparent;
  color: var(--text-secondary);
  border-color: transparent;

  &:hover:not(:disabled) {
    background-color: var(--neutral-100);
    color: var(--text-primary);
  }

  &:active:not(:disabled) {
    background-color: var(--neutral-200);
  }
}

.btn-destructive {
  background-color: var(--error);
  color: var(--error-foreground);
  border-color: var(--error);
  box-shadow: var(--shadow-sm);

  &:hover:not(:disabled) {
    background-color: var(--error-700);
    border-color: var(--error-700);
    box-shadow: var(--shadow-md);
  }

  &:active:not(:disabled) {
    transform: translateY(1px);
    box-shadow: var(--shadow-xs);
  }
}

/* ===================================================================
   7. STANDARDIZED FORM INPUTS
   ================================================================== */

.input-base {
  display: flex;
  align-items: center;
  width: 100%;
  border-radius: var(--radius-md);
  border: 1px solid var(--border-default);
  background-color: var(--surface);
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-regular);
  line-height: var(--line-height-ui);
  transition: all 0.15s ease-in-out;

  &::placeholder {
    color: var(--text-tertiary);
  }

  &:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px var(--primary-50);
  }

  &:disabled {
    background-color: var(--neutral-100);
    color: var(--text-disabled);
    cursor: not-allowed;
  }

  &.error {
    border-color: var(--error);

    &:focus {
      border-color: var(--error);
      box-shadow: 0 0 0 3px var(--error-50);
    }
  }
}

/* Input Sizes */
.input-sm {
  height: var(--input-height-sm);
  padding: 0 var(--space-3);
  font-size: var(--font-size-xs);
}

.input-md {
  height: var(--input-height-md);
  padding: 0 var(--space-4);
  font-size: var(--font-size-sm);
}

.input-lg {
  height: var(--input-height-lg);
  padding: 0 var(--space-6);
  font-size: var(--font-size-base);
}

/* ===================================================================
   8. CARD AND CONTAINER SYSTEM
   ================================================================== */

.card-base {
  background-color: var(--surface-elevated);
  border: 1px solid var(--border-subtle);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
}

.card-interactive {
  transition: all 0.15s ease-in-out;
  cursor: pointer;

  &:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
    border-color: var(--border-default);
  }

  &:active {
    transform: translateY(0);
    box-shadow: var(--shadow-sm);
  }
}

.card-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--border-subtle);
}

.card-content {
  padding: var(--space-6);
}

.card-footer {
  padding: var(--space-6);
  border-top: 1px solid var(--border-subtle);
  background-color: var(--neutral-50);
}

/* ===================================================================
   9. ICON STANDARDIZATION
   ================================================================== */

.icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.icon-sm { width: var(--icon-size-sm); height: var(--icon-size-sm); }
.icon-md { width: var(--icon-size-md); height: var(--icon-size-md); }
.icon-lg { width: var(--icon-size-lg); height: var(--icon-size-lg); }
.icon-xl { width: var(--icon-size-xl); height: var(--icon-size-xl); }

/* ===================================================================
   10. LOADING, EMPTY, AND ERROR STATES
   ================================================================== */

.state-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-12);
  text-align: center;
  gap: var(--space-4);
}

.loading-spinner {
  width: var(--icon-size-lg);
  height: var(--icon-size-lg);
  border: 2px solid var(--border-subtle);
  border-top: 2px solid var(--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.empty-state {
  color: var(--text-secondary);

  .icon {
    color: var(--text-tertiary);
    margin-bottom: var(--space-2);
  }
}

.error-state {
  color: var(--error);

  .icon {
    color: var(--error);
    margin-bottom: var(--space-2);
  }
}
